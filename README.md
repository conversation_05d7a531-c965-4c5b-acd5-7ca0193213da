# infrastructure-modules

Repository for managing [Terraform](https://developer.hashicorp.com/) modules.

## Installation
- macOS and Linux via [Homebrew](https://brew.sh/):
```bash
brew tap hashicorp/tap
brew install hashicorp/tap/terraform
```
- Windows - follow this [link](https://developer.hashicorp.com/terraform/install)

## Development

### Creating your own module
A module is a container for multiple resources that are used together. You can use modules to create lightweight abstractions, so that you can describe your infrastructure in terms of its architecture, rather than directly in terms of physical objects.

To create your own module:
- Follow best practises from [Terraform](https://developer.hashicorp.com/terraform/language/modules/develop/structure) and [GCP](https://cloud.google.com/docs/terraform/best-practices-for-terraform).
- Install [terraform docks](https://github.com/terraform-docs/terraform-docs) for module's readme generation.
```bash
brew install terraform-docs
```
- When your module is ready, to create final documentation, run it as follows
```bash
cd /path/to/module
terraform-docs markdown table --output-file README.md --output-mode inject .
```
More info you can find [here](https://github.com/terraform-docs/terraform-docs).

### Code formatting
#### Run code checks

```bash ⚙️
make code-check
```
If you are using IntelliJ IDE, you can install [File Watchers](https://www.jetbrains.com/help/idea/using-file-watchers.html) plugin and configure it for Terraform to run on a file save.
To improve your experience, you can also install [Terraform and HCL](https://plugins.jetbrains.com/plugin/7808-terraform-and-hcl) plugin.


### Tests
To understand concept of infrastructure testing with Terraform, visit following pages:
- https://developer.hashicorp.com/terraform/tutorials/configuration-language/test
- https://developer.hashicorp.com/terraform/language/tests
- https://developer.hashicorp.com/terraform/cli/commands/test

#### Install Python
Python version 3.12 is used to run some integration tests.

Simple Python Version Management [pyenv](https://github.com/pyenv/pyenv#installation) was used for installing Python
3.12:
1. Install  [pyenv](https://github.com/pyenv/pyenv#installation) if not installed already.
2. Update `pyenv` with `pyenv update`.
3. Look for the latest python 3.12 with the `pyenv install --list | grep 3.12` command.
4. Install the latest python 3.12 and set it as local interpreter:
```bash
pyenv install 3.12.8
pyenv local 3.12.8
```

#### Run unit tests 🧪

```bash
make test-unit
```

#### Run int tests 🧪

```bash
make test-int
```

#### Run tests from specific directory 🧪

```bash
make test-int dir=alert
```

## Module usage

To use the module in a different GitLab repository as a [source of the module](https://developer.hashicorp.com/terraform/language/modules/sources#http-urls), use one of the following URL syntaxes:
```
git::ssh://**************/namespace/terraform/modules/x/y
```
or
```
git::**************:namespace/terraform/modules/x/y
```
If you are trying to specify a directory inside a repository rather than a whole repository,
you will also need to use the [Modules in Package Sub-directories](https://www.terraform.io/language/modules/sources#modules-in-package-sub-directories) syntax to mark the boundary between the repository URL
and the path within the repository (the "subdirectory").

### Real example
Using `storage_bucket` module located in `modules` directory of `infrastructure-modules` GitLab repo `main` branch:

```
git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/storage_bucket?ref=main
```
### GitLab CI/CD authentication to a different repository using Terraform
To be able to access a different GitLab repository as a source for the module definition, you must use an HTTPS connection.

- In your `.gitlab-ci-yml` file place this line in your `script` section:
    ```bash
    git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com".insteadOf "https://gitlab.com"
    ```
- In this repository, make sure you adjust the `Job token permissions` to allow the CI/CD job of the resource module to read from this repository.
  Go to the `Settings` of this repository and then `CI/CD>Job token permission`.
