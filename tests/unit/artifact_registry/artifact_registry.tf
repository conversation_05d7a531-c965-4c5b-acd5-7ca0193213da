# Test artifact registry with keep_latest_tag enabled (default)
module "test_registry_keep_latest_tag_enabled" {
  source          = "../../../src/artifact_registry"
  should_create   = true
  repository_name = "test-registry-keep-latest-tag-enabled"
  repository_type = "DOCKER"
  keep_latest_tag = true
  labels          = { test = "unit", policy = "keep-latest-tag" }
}

# Test artifact registry with keep_latest_tag disabled
module "test_registry_keep_latest_tag_disabled" {
  source          = "../../../src/artifact_registry"
  should_create   = true
  repository_name = "test-registry-keep-latest-tag-disabled"
  repository_type = "DOCKER"
  keep_latest_tag = false
  labels          = { test = "unit", policy = "no-keep-latest-tag" }
}

# Test artifact registry with all cleanup policies
module "test_registry_all_cleanup_policies" {
  source                     = "../../../src/artifact_registry"
  should_create              = true
  repository_name            = "test-registry-all-policies"
  repository_type            = "DOCKER"
  keep_latest_tag            = true
  delete_artifact_older_than = 30
  keep_artifact_count        = 10
  labels                     = { test = "unit", policy = "all-policies" }
}

# Test artifact registry with minimal configuration
module "test_registry_minimal" {
  source          = "../../../src/artifact_registry"
  should_create   = true
  repository_name = "test-registry-minimal"
  repository_type = "DOCKER"
  labels          = { test = "unit", policy = "minimal" }
}

# Test artifact registry with should_create = false
module "test_registry_should_not_create" {
  source          = "../../../src/artifact_registry"
  should_create   = false
  repository_name = "should-not-create-this-registry"
  repository_type = "DOCKER"
}

# Test artifact registry with Maven format
module "test_registry_maven" {
  source          = "../../../src/artifact_registry"
  should_create   = true
  repository_name = "test-registry-maven"
  repository_type = "MAVEN"
  keep_latest_tag = false # keep_latest_tag is typically for Docker repositories
  labels          = { test = "unit", format = "maven" }
}
