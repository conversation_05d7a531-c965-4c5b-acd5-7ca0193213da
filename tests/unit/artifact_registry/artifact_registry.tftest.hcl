run "test_registry_keep_latest_tag_enabled" {
  command = plan
  module {
    source = "./tests/unit/artifact_registry"
  }

  assert {
    condition     = module.test_registry_keep_latest_tag_enabled.repository_name == "test-registry-keep-latest-tag-enabled"
    error_message = "Incorrect repository name for keep_latest_tag enabled test"
  }

  assert {
    condition     = module.test_registry_keep_latest_tag_enabled.repository_url == "europe-west3-docker.pkg.dev/refb-analytics-staging/test-registry-keep-latest-tag-enabled"
    error_message = "Incorrect repository URL for keep_latest_tag enabled test"
  }

  assert {
    condition     = module.test_registry_keep_latest_tag_enabled.labels == tomap({ test = "unit", policy = "keep-latest-tag" })
    error_message = "Incorrect labels for keep_latest_tag enabled test"
  }
}

run "test_registry_keep_latest_tag_disabled" {
  command = plan
  module {
    source = "./tests/unit/artifact_registry"
  }

  assert {
    condition     = module.test_registry_keep_latest_tag_disabled.repository_name == "test-registry-keep-latest-tag-disabled"
    error_message = "Incorrect repository name for keep_latest_tag disabled test"
  }

  assert {
    condition     = module.test_registry_keep_latest_tag_disabled.repository_url == "europe-west3-docker.pkg.dev/refb-analytics-staging/test-registry-keep-latest-tag-disabled"
    error_message = "Incorrect repository URL for keep_latest_tag disabled test"
  }

  assert {
    condition     = module.test_registry_keep_latest_tag_disabled.labels == tomap({ test = "unit", policy = "no-keep-latest-tag" })
    error_message = "Incorrect labels for keep_latest_tag disabled test"
  }
}

run "test_registry_all_cleanup_policies" {
  command = plan
  module {
    source = "./tests/unit/artifact_registry"
  }

  assert {
    condition     = module.test_registry_all_cleanup_policies.repository_name == "test-registry-all-policies"
    error_message = "Incorrect repository name for all cleanup policies test"
  }

  assert {
    condition     = module.test_registry_all_cleanup_policies.repository_url == "europe-west3-docker.pkg.dev/refb-analytics-staging/test-registry-all-policies"
    error_message = "Incorrect repository URL for all cleanup policies test"
  }

  assert {
    condition     = module.test_registry_all_cleanup_policies.labels == tomap({ test = "unit", policy = "all-policies" })
    error_message = "Incorrect labels for all cleanup policies test"
  }
}

run "test_registry_minimal" {
  command = plan
  module {
    source = "./tests/unit/artifact_registry"
  }

  assert {
    condition     = module.test_registry_minimal.repository_name == "test-registry-minimal"
    error_message = "Incorrect repository name for minimal test"
  }

  assert {
    condition     = module.test_registry_minimal.repository_url == "europe-west3-docker.pkg.dev/refb-analytics-staging/test-registry-minimal"
    error_message = "Incorrect repository URL for minimal test"
  }

  assert {
    condition     = module.test_registry_minimal.labels == tomap({ test = "unit", policy = "minimal" })
    error_message = "Incorrect labels for minimal test"
  }
}

run "test_registry_should_not_create" {
  command = plan
  module {
    source = "./tests/unit/artifact_registry"
  }

  assert {
    condition     = module.test_registry_should_not_create.repository_name == null
    error_message = "Repository name should be null when should_create is false"
  }

  assert {
    condition     = module.test_registry_should_not_create.repository_url == null
    error_message = "Repository URL should be null when should_create is false"
  }

  assert {
    condition     = module.test_registry_should_not_create.labels == null
    error_message = "Labels should be null when should_create is false"
  }
}

run "test_registry_maven" {
  command = plan
  module {
    source = "./tests/unit/artifact_registry"
  }

  assert {
    condition     = module.test_registry_maven.repository_name == "test-registry-maven"
    error_message = "Incorrect repository name for Maven test"
  }

  assert {
    condition     = module.test_registry_maven.repository_url == "europe-west3-maven.pkg.dev/refb-analytics-staging/test-registry-maven"
    error_message = "Incorrect repository URL for Maven test"
  }

  assert {
    condition     = module.test_registry_maven.labels == tomap({ test = "unit", format = "maven" })
    error_message = "Incorrect labels for Maven test"
  }
}
