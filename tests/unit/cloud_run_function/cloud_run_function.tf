locals {
  labels = { test = "unit" }
}

resource "google_service_account" "test_function_service_account" {
  account_id = "test-function-v2-sa"
}

module "test_function_registry" {
  source          = "../../../src/artifact_registry"
  should_create   = true
  repository_name = "test-function-v2-registry"
  labels          = local.labels
}

module "test_function_v2_should_create" {
  source                = "../../../src/cloud_run_function"
  force_deploy          = false
  artifact_registry_url = module.test_function_registry.repository_url
  function_name         = "test-function-v2"
  function_entrypoint   = "main"
  function_root_path    = "tests/unit/cloud_run_function/test_function"
  function_common_path  = null
  service_account_email = google_service_account.test_function_service_account.email

  container_cpu    = "1"
  container_memory = "512Mi"

  min_instance_count = 0
  max_instance_count = 10

  env_variables = {
    ENV = "test"
  }

  labels = local.labels
}

module "test_function_v2_with_vpc" {
  source                = "../../../src/cloud_run_function"
  force_deploy          = false
  artifact_registry_url = module.test_function_registry.repository_url
  function_name         = "test-function-v2-vpc"
  function_entrypoint   = "handler"
  function_root_path    = "tests/unit/cloud_run_function/test_function"
  function_common_path  = null
  service_account_email = google_service_account.test_function_service_account.email

  vpc_access = true

  container_cpu    = "2"
  container_memory = "1Gi"

  labels = local.labels
}
