run "test_cloud_run_function_should_create" {
  command = plan
  module {
    source = "./tests/unit/cloud_run_function"
  }

  assert {
    condition     = module.test_function_v2_should_create.function_name == "test-function-v2"
    error_message = "Incorrect function name"
  }

  # Note: function_id, function_url, and function_full_name are not available during plan phase
  # These values are computed after the resource is created
}

run "test_cloud_run_function_with_vpc" {
  command = plan
  module {
    source = "./tests/unit/cloud_run_function"
  }

  assert {
    condition     = module.test_function_v2_with_vpc.function_name == "test-function-v2-vpc"
    error_message = "Incorrect VPC function name"
  }

  # Note: function_id, function_url, and function_full_name are not available during plan phase
  # These values are computed after the resource is created
}
