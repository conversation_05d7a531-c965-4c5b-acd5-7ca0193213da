run "test_bucket_should_create" {
  command = plan
  module {
    source = "./tests/unit/storage_bucket"
  }

  assert {
    condition     = module.test_bucket_should_create.bucket_name == "refb-analytics-staging-test-bucket"
    error_message = "Incorrect bucket name"
  }

  assert {
    condition     = module.test_bucket_should_create.resource_name == "projects/_/buckets/refb-analytics-staging-test-bucket"
    error_message = "Incorrect resource name"
  }

  assert {
    condition     = module.test_bucket_should_create.force_destroy == false
    error_message = "Force destroy should be disabled"
  }

  assert {
    condition     = module.test_bucket_should_create.versioning_enabled == true
    error_message = "Versioning should be enabled"
  }

  assert {
    condition     = module.test_bucket_should_create.public_access_prevention == "enforced"
    error_message = "Public access should be forbidden"
  }

  assert {
    condition     = module.test_bucket_should_create.labels == tomap({ test : "unit" })
    error_message = "Bucket labels should be set"
  }
}

run "test_bucket_should_not_create" {
  command = plan
  module {
    source = "./tests/unit/storage_bucket"
  }

  assert {
    condition     = module.test_bucket_should_not_create.bucket_name == null
    error_message = "Bucket name should be null"
  }

  assert {
    condition     = module.test_bucket_should_not_create.resource_name == null
    error_message = "Resource name should be null"
  }
}
