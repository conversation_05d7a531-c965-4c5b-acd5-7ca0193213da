module "test_bucket_should_create" {
  source                          = "../../../src/storage_bucket"
  should_create                   = true
  bucket_location                 = local.gcp_region
  bucket_name                     = "${local.gcp_project_id}-test-bucket"
  versioning_enabled              = true
  force_destroy                   = false
  bucket_public_access_prevention = "enforced"
  labels                          = { test = "unit" }
}


module "test_bucket_should_not_create" {
  source          = "../../../src/storage_bucket"
  should_create   = false
  bucket_location = local.gcp_region
  bucket_name     = "should-not-create-this-bucket"
}
