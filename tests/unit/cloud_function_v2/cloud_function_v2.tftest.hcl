run "test_function_v2_should_create" {
  command = plan
  module {
    source = "./tests/unit/cloud_function_v2"
  }

  assert {
    condition     = module.test_function_v2_should_create.function_name == "test-function-v2"
    error_message = "Incorrect function name"
  }

  assert {
    condition     = module.test_function_v2_should_create.function_id == "projects/refb-analytics-staging/locations/europe-west3/services/test-function-v2"
    error_message = "Incorrect function id"
  }

  assert {
    condition     = module.test_function_v2_should_create.function_full_name == "namespaces/refb-analytics-staging/services/test-function-v2"
    error_message = "Incorrect function full name"
  }
}

run "test_function_v2_with_vpc" {
  command = plan
  module {
    source = "./tests/unit/cloud_function_v2"
  }

  assert {
    condition     = module.test_function_v2_with_vpc.function_name == "test-function-v2-vpc"
    error_message = "Incorrect VPC function name"
  }

  assert {
    condition     = module.test_function_v2_with_vpc.function_id == "projects/refb-analytics-staging/locations/europe-west3/services/test-function-v2-vpc"
    error_message = "Incorrect VPC function id"
  }
}
