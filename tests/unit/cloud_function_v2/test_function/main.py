import logging
from typing import Any

import functions_framework
from flask import Request


@functions_framework.http
def main(request: Request) -> dict[str, Any]:
    """
    Unit test function for cloud_function_v2 module.
    """
    logging.info("Unit test function called")
    
    return {"body": "Unit test OK", "status": 200}


@functions_framework.http
def handler(request: Request) -> dict[str, Any]:
    """
    Alternative handler for VPC test.
    """
    logging.info("VPC test function called")
    
    return {"body": "VPC test OK", "status": 200}
