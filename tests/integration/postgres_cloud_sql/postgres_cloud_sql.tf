locals {
  data_engineers = toset([
    "tomas<PERSON>.<PERSON><PERSON><PERSON>@refurbed.com",
    "<EMAIL>",
    "krzysztof.s<PERSON><PERSON><PERSON>@refurbed.com"
  ])
}

resource "google_service_account" "test_sql_service_account" {
  account_id = "test-sql-sa-${local.run_id}"
}

module "test_postgres_sql" {
  source              = "../../../src/postgres_cloud_sql"
  should_create       = true
  deletion_protection = false

  instance_name    = "test-sql-instance-${local.run_id}"
  tier             = "db-f1-micro"
  database_version = "POSTGRES_15"
  iam_users        = local.data_engineers
  iam_groups       = ["<EMAIL>", "<EMAIL>"]
  service_accounts = [google_service_account.test_sql_service_account.account_id]

  labels = { test = "int" }
}
