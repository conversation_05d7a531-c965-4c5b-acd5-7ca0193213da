run "test_job" {
  command = apply
  module {
    source = "./tests/integration/cloud_run_job"
  }

  assert {
    condition     = module.test_job.job_name == "test-job-${local.run_id}"
    error_message = "Incorrect job name"
  }

  assert {
    condition     = module.test_job.job_id == "projects/refb-analytics-staging/locations/europe-west3/jobs/test-job-${local.run_id}"
    error_message = "Incorrect job id"
  }

  assert {
    condition     = module.test_job.job_full_name == "namespaces/refb-analytics-staging/jobs/test-job-${local.run_id}"
    error_message = "Incorrect job full name"
  }
}
