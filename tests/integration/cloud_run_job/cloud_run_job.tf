locals {
  labels = { test = "int" }
}

resource "google_service_account" "test_job_service_account" {
  account_id = "test-job-sa-${local.run_id}"
}


module "test_job_registry" {
  source          = "../../../src/artifact_registry"
  should_create   = true
  repository_name = "test-job-registry-${local.run_id}"
  labels          = local.labels
}


module "test_job" {
  source                = "../../../src/cloud_run_job"
  force_deploy          = true
  artifact_registry_url = module.test_job_registry.repository_url
  job_name              = "test-job-${local.run_id}"
  job_root_path         = "tests/integration/cloud_run_job/test_job"
  job_common_path       = null
  service_account_email = google_service_account.test_job_service_account.email

  labels = local.labels
}
