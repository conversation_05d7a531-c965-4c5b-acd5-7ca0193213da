import base64
import json
import logging
import os
from typing import Any

import functions_framework
from cloudevents.http import CloudEvent


@functions_framework.cloud_event
def handle_pubsub(cloud_event: CloudEvent) -> str:
    """
    PubSub-triggered function for cloud_function_v2 module integration test.
    """
    logging.info("PubSub function called")

    # Decode the PubSub message
    pubsub_message = base64.b64decode(cloud_event.data["message"]["data"]).decode()
    message_data = json.loads(pubsub_message)

    logging.info(f"Received PubSub message: {message_data}")

    # Test environment variables
    env_var = os.getenv("ENV", "not_set")
    test_var = os.getenv("TEST_VAR", "not_set")
    function_target = os.getenv("FUNCTION_TARGET", "not_set")

    response_data = {
        "message": "PubSub integration test OK",
        "received_data": message_data,
        "environment": env_var,
        "test_variable": test_var,
        "function_target": function_target,
        "status": 200
    }

    logging.info(f"Response data: {response_data}")

    return "OK"


@functions_framework.http
def handle_http(_: CloudEvent) -> dict[str, Any]:
    """
    HTTP endpoint for testing (fallback).
    """
    logging.info("HTTP function called")

    return {
        "message": "HTTP endpoint OK",
        "status": 200
    }
