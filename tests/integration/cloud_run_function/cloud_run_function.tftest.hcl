run "test_cloud_run_function" {
  command = apply
  module {
    source = "./tests/integration/cloud_run_function"
  }

  assert {
    condition     = module.test_function_v2.function_name == "test-function-v2-${local.run_id}"
    error_message = "Incorrect function name"
  }

  assert {
    condition     = module.test_function_v2.function_id == "projects/refb-analytics-staging/locations/europe-west3/services/test-function-v2-${local.run_id}"
    error_message = "Incorrect function id"
  }

  assert {
    condition     = module.test_function_v2.function_full_name == "namespaces/refb-analytics-staging/services/test-function-v2-${local.run_id}"
    error_message = "Incorrect function full name"
  }

  assert {
    condition     = can(regex("^https://", module.test_function_v2.function_url))
    error_message = "Function URL should start with https://"
  }

  # Test PubSub function
  assert {
    condition     = module.test_function_v2_pubsub.function_name == "test-function-v2-pubsub-${local.run_id}"
    error_message = "Incorrect PubSub function name"
  }

  assert {
    condition     = module.test_function_v2_pubsub.pubsub_subscription_name == "test-function-v2-pubsub-${local.run_id}-trigger-subscription"
    error_message = "Incorrect PubSub subscription name"
  }

  assert {
    condition     = can(regex("^https://", module.test_function_v2_pubsub.function_url))
    error_message = "PubSub function URL should start with https://"
  }

  # Test PubSub topic
  assert {
    condition     = module.test_pubsub_topic.topic_name == "test-function-v2-topic-${local.run_id}"
    error_message = "Incorrect PubSub topic name"
  }
}
