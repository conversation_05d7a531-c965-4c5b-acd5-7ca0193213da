run "test_service_account" {
  command = apply
  module {
    source = "./tests/integration/service_account"
  }

  assert {
    condition     = module.test_service_account.account_id == "test-sa-${local.run_id}"
    error_message = "Incorrect service account id"
  }

  assert {
    condition     = module.test_service_account.sa_email == "test-sa-${local.run_id}@refb-analytics-staging.iam.gserviceaccount.com"
    error_message = "Incorrect service account e-mail"
  }
}
