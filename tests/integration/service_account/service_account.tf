module "test_sa_bucket_1" {
  source          = "../../../src/storage_bucket"
  bucket_location = local.gcp_region
  bucket_name     = "${local.gcp_project_id}-test-sa-bucket-1-${local.run_id}"
}

module "test_sa_bucket_2" {
  source          = "../../../src/storage_bucket"
  bucket_location = local.gcp_region
  bucket_name     = "${local.gcp_project_id}-test-sa-bucket-2-${local.run_id}"
}

module "test_service_account" {
  depends_on = [module.test_sa_bucket_1, module.test_sa_bucket_2]
  source     = "../../../src/service_account"

  sa_name = "test-sa-${local.run_id}"
  predefined_roles = [
    {
      roles          = ["roles/storage.objectViewer", "roles/storage.objectUser"],
      resource_names = [module.test_sa_bucket_1.resource_name, module.test_sa_bucket_2.resource_name]
    },
    { roles = ["roles/logging.viewer"] }
  ]
  custom_permissions = [
    { permissions = ["run.jobs.get"] }, { permissions = ["cloudfunctions.functions.get"] },
    {
      permissions    = ["storage.objects.list"],
      resource_names = [module.test_sa_bucket_1.resource_name, module.test_sa_bucket_2.resource_name]
    }
  ]
}
