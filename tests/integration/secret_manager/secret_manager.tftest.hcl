run "test_secret_manager" {
  command = apply
  module {
    source = "./tests/integration/secret_manager"
  }

  assert {
    condition     = module.test_secret.secret_id == "test-secret-${local.run_id}"
    error_message = "Incorrect secret ID"
  }

  assert {
    condition     = module.test_secret.secret_name == "projects/205674098492/secrets/test-secret-${local.run_id}"
    error_message = "Incorrect secret name"
  }
}
