locals {
  labels = { test = "int" }
}

resource "google_service_account" "test_function_service_account" {
  account_id = "test-function-v2-sa-${local.run_id}"
}

module "test_function_registry" {
  source          = "../../../src/artifact_registry"
  should_create   = true
  repository_name = "test-function-v2-registry-${local.run_id}"
  labels          = local.labels
}

module "test_function_v2" {
  source                = "../../../src/cloud_function_v2"
  force_deploy          = true
  artifact_registry_url = module.test_function_registry.repository_url
  function_name         = "test-function-v2-${local.run_id}"
  function_entrypoint   = "main"
  function_root_path    = "tests/integration/cloud_function_v2/test_function"
  function_common_path  = null
  service_account_email = google_service_account.test_function_service_account.email

  container_cpu    = "1"
  container_memory = "512Mi"

  min_instance_count = 0
  max_instance_count = 5

  env_variables = {
    ENV      = "integration_test"
    TEST_VAR = "test_value"
  }

  labels = local.labels
}

# PubSub topic for testing
module "test_pubsub_topic" {
  source        = "../../../src/pub_sub"
  should_create = true
  topic_name    = "test-function-v2-topic-${local.run_id}"
  labels        = local.labels
}

# PubSub-triggered function
module "test_function_v2_pubsub" {
  source                = "../../../src/cloud_function_v2"
  force_deploy          = true
  artifact_registry_url = module.test_function_registry.repository_url
  function_name         = "test-function-v2-pubsub-${local.run_id}"
  function_entrypoint   = "handle_pubsub"
  function_root_path    = "tests/integration/cloud_function_v2/test_pubsub_function"
  function_common_path  = null
  service_account_email = google_service_account.test_function_service_account.email

  container_cpu    = "1"
  container_memory = "512Mi"

  min_instance_count = 0
  max_instance_count = 5

  # PubSub trigger configuration
  pubsub_trigger = {
    topic_name           = module.test_pubsub_topic.topic_name
    ack_deadline_seconds = 300
    retry_policy         = "RETRY_POLICY_DO_NOT_RETRY"
  }

  env_variables = {
    ENV      = "integration_test_pubsub"
    TEST_VAR = "pubsub_test_value"
  }

  labels = local.labels
}
