run "test_function_v2" {
  command = apply
  module {
    source = "./tests/integration/cloud_function_v2"
  }

  assert {
    condition     = module.test_function_v2.function_name == "test-function-v2-${local.run_id}"
    error_message = "Incorrect function name"
  }

  assert {
    condition     = module.test_function_v2.function_id == "projects/refb-analytics-staging/locations/europe-west3/services/test-function-v2-${local.run_id}"
    error_message = "Incorrect function id"
  }

  assert {
    condition     = module.test_function_v2.function_full_name == "namespaces/refb-analytics-staging/services/test-function-v2-${local.run_id}"
    error_message = "Incorrect function full name"
  }

  assert {
    condition     = can(regex("^https://", module.test_function_v2.function_url))
    error_message = "Function URL should start with https://"
  }
}
