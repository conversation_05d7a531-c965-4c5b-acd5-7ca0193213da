import logging
import os
from typing import Any

import functions_framework
from flask import Request


@functions_framework.http
def main(request: Request) -> dict[str, Any]:
    """
    Integration test function for cloud_function_v2 module.
    """
    logging.info("Integration test function called")
    
    # Test environment variables
    env_var = os.getenv("ENV", "not_set")
    test_var = os.getenv("TEST_VAR", "not_set")
    function_target = os.getenv("FUNCTION_TARGET", "not_set")
    
    response_data = {
        "message": "Integration test OK",
        "environment": env_var,
        "test_variable": test_var,
        "function_target": function_target,
        "status": 200
    }
    
    logging.info(f"Response data: {response_data}")
    
    return response_data
