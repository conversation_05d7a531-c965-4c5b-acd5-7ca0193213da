resource "google_service_account" "test_function_service_account" {
  account_id = "test-function-sa-${local.run_id}"
}

module "test_function_artifact_bucket" {
  source          = "../../../src/storage_bucket"
  bucket_location = local.gcp_region
  bucket_name     = "${local.gcp_project_id}-test-function-artifacts-${local.run_id}"
}

module "test_function" {
  source       = "../../../src/cloud_function"
  force_deploy = true

  artifact_bucket_name  = module.test_function_artifact_bucket.bucket_name
  function_name         = "test-function-${local.run_id}"
  function_root_path    = "tests/integration/cloud_function/test_function"
  common_path           = null
  service_account_email = google_service_account.test_function_service_account.email

  service_config = {
    max_instance_count = 1
    available_memory   = "256Mi"
  }

  labels = { test = "int" }
}
