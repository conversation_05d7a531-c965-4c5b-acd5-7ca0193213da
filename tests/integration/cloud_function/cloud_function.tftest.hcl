run "test_function" {
  command = apply
  module {
    source = "./tests/integration/cloud_function"
  }

  assert {
    condition     = module.test_function.function_name == "test-function-${local.run_id}"
    error_message = "Incorrect function name"
  }

  assert {
    condition     = module.test_function.function_id == "projects/refb-analytics-staging/locations/europe-west3/functions/test-function-${local.run_id}"
    error_message = "Incorrect function id"
  }

  assert {
    condition     = module.test_function.function_url == "https://europe-west3-refb-analytics-staging.cloudfunctions.net/test-function-${local.run_id}"
    error_message = "Incorrect function URL"
  }
}
