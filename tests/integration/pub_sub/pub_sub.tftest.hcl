run "test_pub_sub" {
  command = apply
  module {
    source = "./tests/integration/pub_sub"
  }

  assert {
    condition     = module.test_topic.topic_name == "test-topic-${local.run_id}"
    error_message = "Incorrect topic name"
  }

  assert {
    condition     = module.test_topic.topic_id == "projects/refb-analytics-staging/topics/test-topic-${local.run_id}"
    error_message = "Incorrect topic ID"
  }
}
