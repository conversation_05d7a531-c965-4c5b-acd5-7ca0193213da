resource "google_service_account" "test_workflow_service_account" {
  account_id = "test-workflow-sa-${local.run_id}"
}

module "test_workflow_function_artifact_bucket" {
  source          = "../../../src/storage_bucket"
  bucket_location = local.gcp_region
  bucket_name     = "test-workflow-function-artifacts-${local.run_id}"
}

module "test_workflow_function" {
  source       = "../../../src/cloud_function"
  force_deploy = true

  artifact_bucket_name  = module.test_workflow_function_artifact_bucket.bucket_name
  function_name         = "test-wokflow-function-${local.run_id}"
  function_root_path    = "tests/integration/workflow/test_workflow_function"
  common_path           = null
  service_account_email = google_service_account.test_workflow_service_account.email

  service_config = {
    max_instance_count = 1
    available_memory   = "256Mi"
  }

  labels = { test = "int" }
}
