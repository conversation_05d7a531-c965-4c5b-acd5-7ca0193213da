run "test_workflow" {
  command = apply
  module {
    source = "./tests/integration/workflow"
  }

  assert {
    condition     = module.test_workflow.workflow_id == "projects/refb-analytics-staging/locations/europe-west3/workflows/integartion-test-workflow-${local.run_id}"
    error_message = "Incorrect workflow id"
  }

  assert {
    condition     = module.test_workflow.workflow_name == "integartion-test-workflow-${local.run_id}"
    error_message = "Incorrect workflow name"
  }

  assert {
    condition     = module.test_workflow.workflow_http_post_url == "https://workflowexecutions.googleapis.com/v1/projects/refb-analytics-staging/locations/europe-west3/workflows/integartion-test-workflow-${local.run_id}/executions"
    error_message = "Incorrect workflow HTTP POST url"
  }

  assert {
    condition     = module.test_workflow.workflow_landing_page_url == "https://console.cloud.google.com/workflows/workflow/europe-west3/integartion-test-workflow-${local.run_id}/executions?project=refb-analytics-staging"
    error_message = "Incorrect workflow landing page url"
  }
}
