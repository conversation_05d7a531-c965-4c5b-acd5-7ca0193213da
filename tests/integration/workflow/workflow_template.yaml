# $schema: https://json.schemastore.org/workflows.json
main:
  params: [input]
  steps:
    - init:
        assign:
          - result: {}
          - workflow_execution:
              workflow_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_ID")}
              execution_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_EXECUTION_ID")}
              start: ${sys.now()}
              end: ${sys.now()}
              result: {}
              error: null
    - run_workflow:
        try:
          steps:
            - run_function:
                call: run_http_function
                args:
                  function:
                    id: ${sys.get_env("test_function_name")}
                    url: ${sys.get_env("test_function_url")}
                    body: ${workflow_execution}
                result: test_function_result
        except:
          as: e
          steps:
            - assign_workflow_error:
                assign:
                  - workflow_execution.result: ${result}
                  - workflow_execution.error: ${e}
                  - workflow_execution.end: ${sys.now()}
            - raiseError:
                raise: ${workflow_execution}
    - assign_workflow_result:
        assign:
          - workflow_execution.result: ${result}
          - workflow_execution.end: ${sys.now()}
    - done:
        return: ${result}

run_http_function:
  params: [function]
  steps:
    - defaults:
        assign:
          - default_body: {}
          - default_retries: 3
    - log_start:
        call: sys.log
        args:
          data: ${"Running -> " + function.id}
          severity: "INFO"
    - run_function:
        # https://cloud.google.com/workflows/docs/reference/stdlib/http/post
        call: http.post
        args:
          url: ${function.url}
          timeout: 1800 # 30 minutes in seconds
          body: ${default(map.get(function, "body"), default_body)}
          auth:
            type: OIDC
            audience: ${function.url}
        result: run_function_result
    - log_finish:
        call: sys.log
        args:
          data: ${"Finished -> " + function.id}
          severity: "INFO"
    - run_http_function_return:
        return: ${run_function_result}
