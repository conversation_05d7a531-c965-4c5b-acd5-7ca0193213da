resource "google_service_account" "test_bucket_service_account" {
  account_id = "test-bucket-sa-${local.run_id}"
}

resource "google_project_iam_custom_role" "test_reader" {
  role_id = "testBucketReader${local.run_id}"
  title   = "test-bucket-reader-${local.run_id}"

  permissions = ["storage.objects.list", "storage.objects.get"]
}

module "test_bucket" {
  source          = "../../../src/storage_bucket"
  bucket_location = local.gcp_region
  bucket_name     = "${local.gcp_project_id}-test-bucket-${local.run_id}"

  lifecycle_rules = [
    {
      action = {
        type = "Delete"
      }
      condition = {
        age        = 30 # days
        with_state = "ANY"
      }
    }
  ]

  permissions = [
    {
      sa_email = google_service_account.test_bucket_service_account.email
      roles    = [google_project_iam_custom_role.test_reader.name]
    }
  ]

  versioning_enabled              = true
  force_destroy                   = false
  bucket_public_access_prevention = "enforced"
}
