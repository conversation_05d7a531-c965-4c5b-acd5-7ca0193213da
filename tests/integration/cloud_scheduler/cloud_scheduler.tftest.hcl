run "test_scheduler" {
  command = apply
  module {
    source = "./tests/integration/cloud_scheduler"
  }

  assert {
    condition     = module.test_function_scheduler.function_schedule_cron == "1 * * * *"
    error_message = "Incorrect function cron"
  }

  assert {
    condition     = module.test_function_scheduler.function_schedule_time_zone == "Europe/Vienna"
    error_message = "Incorrect function time zone"
  }

  assert {
    condition     = module.test_job_scheduler.job_schedule_cron == "2 * * * *"
    error_message = "Incorrect job cron"
  }

  assert {
    condition     = module.test_job_scheduler.job_schedule_time_zone == "Europe/Warsaw"
    error_message = "Incorrect job time zone"
  }
}
