locals {
  labels = { test = "int" }
}

resource "google_service_account" "test_scheduler_service_account" {
  account_id = "test-scheduler-sa-${local.run_id}"
}

module "test_function_scheduler" {
  source               = "../../../src/cloud_scheduler"
  schedule_name        = "test-function-scheduler-${local.run_id}"
  cron                 = "1 * * * *"
  job_subscriber       = null
  function_subscribers = [google_service_account.test_scheduler_service_account.email]
  enabled              = true
  time_zone            = "Europe/Vienna"

  labels = local.labels
}

module "test_job_registry" {
  source          = "../../../src/artifact_registry"
  should_create   = true
  repository_name = "test-job-scheduler-registry-${local.run_id}"
  labels          = local.labels
}

module "test_scheduler_job" {
  source                = "../../../src/cloud_run_job"
  force_deploy          = true
  artifact_registry_url = module.test_job_registry.repository_url
  job_name              = "test-job-scheduler-${local.run_id}"
  job_root_path         = "tests/integration/cloud_scheduler/test_job"
  job_common_path       = null
  service_account_email = google_service_account.test_scheduler_service_account.email

  labels = local.labels
}

module "test_job_scheduler" {
  source        = "../../../src/cloud_scheduler"
  schedule_name = "test-job-scheduler-${local.run_id}"
  cron          = "2 * * * *"
  job_subscriber = {
    job_name = module.test_scheduler_job.job_name, sa_email = google_service_account.test_scheduler_service_account.email
  }
  function_subscribers = []
  enabled              = true
  time_zone            = "Europe/Warsaw"

  labels = local.labels
}
