include:
  - remote: 'https://gitlab.com/gitlab-com/gl-security/security-operations/infrastructure-security-public/oidc-modules/-/raw/3.1.2/templates/gcp_auth.yaml'

# List of stages for jobs, and their order of execution: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
stages:
  - 🧪 lint & test
  - 🧐 integration test
  - 📨 notify

variables:
  GIT_DEPTH: 10 # https://docs.gitlab.com/ee/user/project/repository/monorepos/#shallow-cloning
  GOOGLE_PROJECT_NO: ************
  GOOGLE_PROJECT_ID: "refb-analytics-staging"
  GCP_REGION: "europe-west3"
  CICD_BUILD_IMAGE: ${GCP_REGION}-docker.pkg.dev/${GOOGLE_PROJECT_ID}/cicd-artifacts/infra-modules

image: ${CICD_BUILD_IMAGE}

.gitlab-oidc:
  extends: .google-oidc:auth
  variables:
    WI_POOL_PROVIDER: //iam.googleapis.com/projects/${GOOGLE_PROJECT_NO}/locations/global/workloadIdentityPools/gitlab-pool-oidc-infra-modules/providers/gitlab-jwt-infra-modules
    SERVICE_ACCOUNT: infrastructure-modules-cicd@${GOOGLE_PROJECT_ID}.iam.gserviceaccount.com
    GCLOUD_PROJECT: ${GOOGLE_PROJECT_ID}

workflow:
  rules:
    - if: $CI_COMMIT_TAG
      when: never
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - when: always

# https://stackoverflow.com/a/********
.step_rules:
  rules:
    - &main_branch_rule
      if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - &feature_branch_rule
      if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      when: manual

lint-code:
  stage: 🧪 lint & test
  extends: .gitlab-oidc
  script:
    - make code-check

test-unit:
  stage: 🧪 lint & test
  extends: .gitlab-oidc
  script:
    - make test-unit

alert:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - make test-int dir=alert
  rules:
    - *main_branch_rule
    - *feature_branch_rule

artifact-registry:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - make test-int dir=artifact_registry
  rules:
    - *main_branch_rule
    - *feature_branch_rule

bq-permissions:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - make test-int dir=bigquery_permissions
  rules:
    - *main_branch_rule
    - *feature_branch_rule

cloud-function:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - make test-int dir=cloud_function
  rules:
    - *main_branch_rule
    - *feature_branch_rule

cloud-run-job:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - export HOME=/tmp # To avoid errors: Read-only file system: '/root/.docker/
    - make test-int dir=cloud_run_job
  rules:
    - *main_branch_rule
    - *feature_branch_rule

cloud-run-function:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - export HOME=/tmp # To avoid errors: Read-only file system: '/root/.docker/
    - make test-int dir=cloud_run_function
  rules:
    - *main_branch_rule
    - *feature_branch_rule

cloud-scheduler:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - export HOME=/tmp # To avoid errors: Read-only file system: '/root/.docker/
    - make test-int dir=cloud_scheduler
  rules:
    - *main_branch_rule
    - *feature_branch_rule

storage-bucket:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - make test-int dir=storage_bucket
  rules:
    - *main_branch_rule
    - *feature_branch_rule

postgres-cloud-sql:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - make test-int dir=postgres_cloud_sql
  rules:
    - *main_branch_rule
    - *feature_branch_rule

pub-sub:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - make test-int dir=pub_sub
  rules:
    - *main_branch_rule
    - *feature_branch_rule

secret-manager:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - make test-int dir=secret_manager
  rules:
    - *main_branch_rule
    - *feature_branch_rule

service-account:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - make test-int dir=service_account
  rules:
    - *main_branch_rule
    - *feature_branch_rule

workflows:
  stage: 🧐 integration test
  extends: .gitlab-oidc
  script:
    - make test-int dir=workflow
  rules:
    - *main_branch_rule
    - *feature_branch_rule

notify-failed:
  stage: 📨 notify
  when: on_failure
  script:
    - |
      export MM_MESSAGE=":exclamation: **GitLab pipeline has failed:** <$CI_PIPELINE_URL|$CI_PROJECT_NAME>\
      \n**User:** $GITLAB_USER_NAME\
      \n**Date:** $CI_JOB_STARTED_AT"
    - echo $MM_MESSAGE
    - |
      curl -i -X POST -H 'Content-type: application/json' --data "{
        \"text\": \"$MM_MESSAGE\"
      }" $MM_WEBHOOK;
  only:
    - main
