#!/bin/bash

# Test script for cloud_run_function module
# Usage: ./test_cloud_run_function.sh <function_url> [test_data]

set -euo pipefail

FUNCTION_URL="${1:-}"
TEST_DATA="${2:-{\"test\": \"Hello from test script!\"}}"

if [ -z "$FUNCTION_URL" ]; then
    echo "Usage: $0 <function_url> [test_data]"
    echo ""
    echo "Examples:"
    echo "  $0 https://my-function-abc123.run.app"
    echo "  $0 https://my-function-abc123.run.app '{\"name\": \"test\"}'"
    echo ""
    echo "Get function URL from Terraform:"
    echo "  terraform output -raw function_url"
    exit 1
fi

echo "🧪 Testing Cloud Run Function"
echo "URL: $FUNCTION_URL"
echo "Data: $TEST_DATA"
echo ""

echo "📡 Making HTTP request..."
echo ""

# Test with curl and show response
curl -w "\n\n📊 Response Details:\n  Status: %{http_code}\n  Time: %{time_total}s\n  Size: %{size_download} bytes\n" \
     -X POST "$FUNCTION_URL" \
     -H "Content-Type: application/json" \
     -H "User-Agent: CloudRunFunctionTester/1.0" \
     -d "$TEST_DATA" \
     --fail-with-body

echo ""
echo "✅ Test completed!"
