locals {
  gcp_project_id = "refb-analytics-staging"
  gcp_region     = "europe-west3"
  run_id         = random_id.test_suffix.dec
}

terraform {
  # https://developer.hashicorp.com/terraform/tutorials/configuration-language/test#prerequisites
  required_version = ">= v1.7.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.16.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 6.16.0"
    }
  }
}

provider "google" {
  project = local.gcp_project_id
  region  = local.gcp_region
}

provider "google-beta" {
  project = local.gcp_project_id
  region  = local.gcp_region
}

data "google_project" "project" {
}

resource "random_id" "test_suffix" {
  byte_length = 4

  keepers = {
    change_on_every_run = timestamp()
  }
}
