SHELL :=./make.sh
$(DEBUG).SILENT: ; # no need for @, DEBUG=yes make ... disable silence

init: ## Initializes terraform state for 'refb-analytics-staging' GCP project
	terraform init

init-check: ## Installs pre-commit prerequisite for code check
	if [ ! -d .venv ]; then python -m venv .venv >/dev/null; fi \
	&& source .venv/bin/activate >/dev/null \
	&& pip install --upgrade pip >/dev/null \
	&& pip install pre-commit >/dev/null

code-check: init init-check ## Runs all linters and reformats code if possible
	source .venv/bin/activate && pre-commit run --all-files && terraform validate

refresh-docs: ## Refreshes terraform docs
	for dir in $(wildcard ./src/*); do \
		pushd "$${dir}" >/dev/null; \
		echo "Updating docs for $${dir}..."; \
		terraform-docs markdown table --output-file README.md --output-mode inject . ;\
		popd >/dev/null; \
	done; \

test-unit: ## Runs unit tests
	./test.sh unit $(dir)

test-int: ## Runs int tests
	./test.sh integration $(dir)

test: test-unit test-int ## Runs all tests

# Auto-document our Makefile using a trick from https://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
.DEFAULT_GOAL := help
help: Makefile
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
