fail_fast: true

repos:
  # Some out-of-the-box hooks for pre-commit
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-added-large-files
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.35.1
    hooks:
      - id: yamllint
        name: yaml
        args: ['-d {extends: relaxed, rules: {line-length: {max: 120}}}', '-s']

  # Bash shellcheck
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.10.0.1
    hooks:
      - id: shellcheck
        name: bash-shellcheck

  # Terraform
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.97.0
    hooks:
      - id: terraform_fmt
        name: terraform-fmt
        args:
          - --args=-recursive
          - --hook-config=--parallelism-ci-cpu-cores=4
