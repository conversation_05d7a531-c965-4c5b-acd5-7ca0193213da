variable "viewers" {
  description = "Permissions block for dataset viewers, ref. https://cloud.google.com/bigquery/docs/access-control#bigquery.dataViewer"
  type = list(object({
    dataset = string,
    users   = optional(list(string), [])
    groups  = optional(list(string), [])
  }))
  default = []
}

variable "editors" {
  description = "Permissions block for dataset editors. ref. https://cloud.google.com/bigquery/docs/access-control#bigquery.dataEditor"
  type = list(object({
    dataset = string,
    users   = optional(list(string), [])
    groups  = optional(list(string), [])
  }))
  default = []
}

variable "admins" {
  description = "Permissions block for dataset admins, ref. https://cloud.google.com/bigquery/docs/access-control#bigquery.admin"
  type = list(object({
    dataset = string,
    users   = optional(list(string), [])
    groups  = optional(list(string), [])
  }))
  default = []
}
