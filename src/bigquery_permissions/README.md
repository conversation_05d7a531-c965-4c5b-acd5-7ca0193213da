# BigQuery permissions module

A module to simplify BigQuery permission management for users.

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_bigquery_dataset_iam_member.admins](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_dataset_iam_member) | resource |
| [google_bigquery_dataset_iam_member.editors](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_dataset_iam_member) | resource |
| [google_bigquery_dataset_iam_member.viewers](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_dataset_iam_member) | resource |
| [google_project_iam_member.viewers-editors-job](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_client_config.config](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/client_config) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_admins"></a> [admins](#input\_admins) | Permissions block for dataset admins, ref. https://cloud.google.com/bigquery/docs/access-control#bigquery.admin | <pre>list(object({<br/>    dataset = string,<br/>    users   = optional(list(string), [])<br/>    groups  = optional(list(string), [])<br/>  }))</pre> | `[]` | no |
| <a name="input_editors"></a> [editors](#input\_editors) | Permissions block for dataset editors. ref. https://cloud.google.com/bigquery/docs/access-control#bigquery.dataEditor | <pre>list(object({<br/>    dataset = string,<br/>    users   = optional(list(string), [])<br/>    groups  = optional(list(string), [])<br/>  }))</pre> | `[]` | no |
| <a name="input_viewers"></a> [viewers](#input\_viewers) | Permissions block for dataset viewers, ref. https://cloud.google.com/bigquery/docs/access-control#bigquery.dataViewer | <pre>list(object({<br/>    dataset = string,<br/>    users   = optional(list(string), [])<br/>    groups  = optional(list(string), [])<br/>  }))</pre> | `[]` | no |

## Outputs

No outputs.
<!-- END_TF_DOCS -->
