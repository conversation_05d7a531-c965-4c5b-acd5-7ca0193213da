locals {
  viewer_users = merge([
    for id, item in var.viewers : {
      for email_id, email in item.users :
      "${id}-user-${email_id}" => { dataset = item.dataset, member = "user:${email}" }
    }
  ]...)

  viewer_groups = merge([
    for id, item in var.viewers : {
      for email_id, email in item.groups :
      "${id}-group-${email_id}" => { dataset = item.dataset, member = "group:${email}" }
    }
  ]...)

  editor_users = merge([
    for id, item in var.editors : {
      for email_id, email in item.users :
      "${id}-user-${email_id}" => { dataset = item.dataset, member = "user:${email}" }
    }
  ]...)

  editor_groups = merge([
    for id, item in var.editors : {
      for email_id, email in item.groups :
      "${id}-group-${email_id}" => { dataset = item.dataset, member = "group:${email}" }
    }
  ]...)

  admin_users = merge([
    for id, item in var.admins : {
      for email_id, email in item.users :
      "${id}-user-${email_id}" => { dataset = item.dataset, member = "user:${email}" }
    }
  ]...)

  admin_groups = merge([
    for id, item in var.admins : {
      for email_id, email in item.groups :
      "${id}-group-${email_id}" => { dataset = item.dataset, member = "group:${email}" }
    }
  ]...)
}

data "google_client_config" "config" {}

resource "google_bigquery_dataset_iam_member" "viewers" {
  for_each   = merge(local.viewer_users, local.viewer_groups)
  dataset_id = each.value["dataset"]
  role       = "roles/bigquery.dataViewer"
  member     = each.value["member"]
}

resource "google_bigquery_dataset_iam_member" "editors" {
  for_each   = merge(local.editor_users, local.editor_groups)
  dataset_id = each.value["dataset"]
  role       = "roles/bigquery.dataEditor"
  member     = each.value["member"]
}

resource "google_bigquery_dataset_iam_member" "admins" {
  for_each   = merge(local.admin_users, local.admin_groups)
  dataset_id = each.value["dataset"]
  role       = "roles/bigquery.admin"
  member     = each.value["member"]
}

# Can be granted on a project level only
resource "google_project_iam_member" "viewers-editors-job" {
  for_each = merge(local.viewer_users, local.viewer_groups, local.editor_users, local.editor_groups)
  project  = data.google_client_config.config.project
  role     = "roles/bigquery.jobUser"
  member   = each.value["member"]
}
