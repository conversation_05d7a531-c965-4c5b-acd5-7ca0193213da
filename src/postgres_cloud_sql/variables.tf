variable "instance_name" {
  description = "Name of the instance that will be followed by random suffix."
  type        = string
}

variable "detabase_name" {
  description = "Name of an empty db to be created."
  type        = string
  default     = "platform"
}

variable "database_version" {
  description = "Postgres version, ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/sql_database_instance#database_version"
  type        = string
}

variable "tier" {
  description = "Machine type, ref. https://cloud.google.com/sql/pricing#instance-pricing, for custom CPU/memory please use 'db-custom-<CPUs>-<Memory_in_MB>'"
  type        = string
}

variable "availability_type" {
  description = "The availability type of the Cloud SQL instance, high availability (REGIONAL) or single zone (ZONAL)."
  type        = string
  default     = "ZON<PERSON>"
}

variable "iam_users" {
  description = "An optional list of IAM users emails."
  type        = set(string)
  default     = []
}

variable "iam_groups" {
  description = "An optional list of IAM group emails."
  type        = set(string)
  default     = []
}

variable "service_accounts" {
  description = "An optional list of service account names."
  type        = set(string)
  default     = []
}

variable "deletion_protection" {
  description = "Enables deletion protection of an instance."
  type        = bool
  default     = false
}

variable "should_create" {
  description = "Allows to create all necessary resources in a module. To be used for conditional creation of a module."
  type        = bool
  default     = true
}

variable "labels" {
  description = "A set of key/value label pairs associated with this module"
  type        = map(string)
  default     = null
}
