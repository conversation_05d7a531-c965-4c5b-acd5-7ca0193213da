locals {
  gcp_project_id     = data.google_client_config.config.project
  iam_users          = var.should_create ? var.iam_users : []
  iam_groups         = var.should_create ? var.iam_groups : []
  instance_name      = one(google_sql_database_instance.this.*.name)
  full_instance_name = local.instance_name != null ? "projects/${local.gcp_project_id}/instances/${local.instance_name}" : null
  sa_emails          = { for id, sa in tolist(var.service_accounts) : id => "${sa}@${local.gcp_project_id}.iam.gserviceaccount.com" }

  # roles/cloudsql.client: access to Cloud SQL instances from e.g. Cloud SQL Auth Proxy: not required for accessing an instance using IP addresses
  # roles/cloudsql.instanceUser: access to a Cloud SQL instance for IAM users when cloudsql.iam_authentication is set to ON
  iam_roles = ["roles/cloudsql.client", "roles/cloudsql.instanceUser"]

  iam_principals = merge(
    { for user in local.iam_users : user => { email = user, type = "CLOUD_IAM_USER", member = "user:${user}" } },
    { for group in local.iam_groups : group => { email = group, type = "CLOUD_IAM_GROUP", member = "group:${group}" } }
  )

  iam_principals_roles = merge([
    for id, principal in local.iam_principals : {
      for role_id, role in local.iam_roles :
      "${id}-${principal["type"]}-${role_id}" => { email = principal["email"], role = role, type = principal["type"], member = principal["member"] }
    }
  ]...)
}

data "google_client_config" "config" {}

resource "random_id" "instance_name_suffix" {
  count = var.should_create ? 1 : 0

  byte_length = 4
}

resource "random_password" "root_password" {
  count = var.should_create ? 1 : 0

  length           = 16
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"
  keepers = {
    trigger = var.instance_name
  }
}

resource "google_sql_database_instance" "this" {
  count = var.should_create ? 1 : 0

  name             = "${var.instance_name}-${random_id.instance_name_suffix[0].hex}"
  database_version = var.database_version

  settings {
    tier              = var.tier
    availability_type = var.availability_type

    database_flags {
      name  = "cloudsql.iam_authentication"
      value = "on"
    }
    deletion_protection_enabled = var.deletion_protection
  }

  deletion_protection = var.deletion_protection
  root_password       = random_password.root_password[0].result
}

# To fix terraform bug: https://github.com/hashicorp/terraform-provider-google/issues/14233#issuecomment-**********
resource "time_sleep" "sleep" {
  count = var.should_create ? 1 : 0

  depends_on = [google_sql_database_instance.this]

  create_duration = "30s"
}

resource "google_sql_user" "iam_user" {
  depends_on = [time_sleep.sleep]

  for_each = local.iam_principals
  name     = each.value["email"]
  instance = local.instance_name
  type     = each.value["type"]
}

resource "google_project_iam_member" "user_permissions" {
  depends_on = [google_sql_user.iam_user]

  for_each = local.iam_principals_roles
  project  = local.gcp_project_id
  role     = each.value["role"]
  member   = each.value["member"]

  condition {
    expression = "resource.name.startsWith('${local.full_instance_name}')"
    title      = "Access limited to the ${local.full_instance_name}"
  }
}

# service account client access
resource "google_project_iam_member" "sa_permissions" {
  for_each = local.sa_emails
  project  = local.gcp_project_id
  role     = "roles/cloudsql.client"
  member   = "serviceAccount:${each.value}"

  dynamic "condition" {
    for_each = local.instance_name != null ? [local.full_instance_name] : []
    iterator = resource
    content {
      expression = "resource.name.startsWith('${resource.value}')"
      title      = "Access limited to the '${resource.value}'"
    }
  }
}

resource "google_sql_database" "empty_db" {
  count = var.should_create ? 1 : 0

  name     = var.detabase_name
  instance = google_sql_database_instance.this[0].name
}

resource "google_secret_manager_secret" "analytics_db_secret" {
  count = var.should_create ? 1 : 0

  secret_id = "${var.instance_name}-db-postgres"

  replication {
    auto {}
  }
  labels = var.labels
}

resource "google_secret_manager_secret_version" "analytics_db_secret_version" {
  count = var.should_create ? 1 : 0

  secret      = google_secret_manager_secret.analytics_db_secret[0].id
  secret_data = <<EOF
  {
    "host": "${google_sql_database_instance.this[0].connection_name}",
    "database": "${google_sql_database.empty_db[0].name}",
    "username": "postgres",
    "password": "${google_sql_database_instance.this[0].root_password}",
    "port": "5432"
  }
  EOF
}
