# Postgres Cloud SQL

A module to abstract basic configuration for GCP Cloud SQL Postgres flavour DB with GCP IAM integration.

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |
| <a name="provider_random"></a> [random](#provider\_random) | n/a |
| <a name="provider_time"></a> [time](#provider\_time) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_project_iam_member.sa_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_project_iam_member.user_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_secret_manager_secret.analytics_db_secret](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret) | resource |
| [google_secret_manager_secret_version.analytics_db_secret_version](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret_version) | resource |
| [google_sql_database.empty_db](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/sql_database) | resource |
| [google_sql_database_instance.this](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/sql_database_instance) | resource |
| [google_sql_user.iam_user](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/sql_user) | resource |
| [random_id.instance_name_suffix](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/id) | resource |
| [random_password.root_password](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/password) | resource |
| [time_sleep.sleep](https://registry.terraform.io/providers/hashicorp/time/latest/docs/resources/sleep) | resource |
| [google_client_config.config](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/client_config) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_availability_type"></a> [availability\_type](#input\_availability\_type) | The availability type of the Cloud SQL instance, high availability (REGIONAL) or single zone (ZONAL). | `string` | `"ZONAL"` | no |
| <a name="input_database_version"></a> [database\_version](#input\_database\_version) | Postgres version, ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/sql_database_instance#database_version | `string` | n/a | yes |
| <a name="input_deletion_protection"></a> [deletion\_protection](#input\_deletion\_protection) | Enables deletion protection of an instance. | `bool` | `false` | no |
| <a name="input_detabase_name"></a> [detabase\_name](#input\_detabase\_name) | Name of an empty db to be created. | `string` | `"platform"` | no |
| <a name="input_iam_groups"></a> [iam\_groups](#input\_iam\_groups) | An optional list of IAM group emails. | `set(string)` | `[]` | no |
| <a name="input_iam_users"></a> [iam\_users](#input\_iam\_users) | An optional list of IAM users emails. | `set(string)` | `[]` | no |
| <a name="input_instance_name"></a> [instance\_name](#input\_instance\_name) | Name of the instance that will be followed by random suffix. | `string` | n/a | yes |
| <a name="input_labels"></a> [labels](#input\_labels) | A set of key/value label pairs associated with this module | `map(string)` | `null` | no |
| <a name="input_service_accounts"></a> [service\_accounts](#input\_service\_accounts) | An optional list of service account names. | `set(string)` | `[]` | no |
| <a name="input_should_create"></a> [should\_create](#input\_should\_create) | Allows to create all necessary resources in a module. To be used for conditional creation of a module. | `bool` | `true` | no |
| <a name="input_tier"></a> [tier](#input\_tier) | Machine type, ref. https://cloud.google.com/sql/pricing#instance-pricing, for custom CPU/memory please use 'db-custom-<CPUs>-<Memory\_in\_MB>' | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_connection_name"></a> [connection\_name](#output\_connection\_name) | SQL instance connection name |
| <a name="output_instance_name"></a> [instance\_name](#output\_instance\_name) | SQL instance name |
<!-- END_TF_DOCS -->
