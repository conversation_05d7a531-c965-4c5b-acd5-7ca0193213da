data "google_client_config" "default" {}

locals {
  region  = data.google_client_config.default.region
  project = data.google_client_config.default.project
  # Whether to include common or not
  source_file_paths = var.include_common ? concat(var.source_file_paths, ["workflows/common.yaml"]) : var.source_file_paths
}

resource "google_project_service" "googleapis" {
  project = local.project
  service = "workflows.googleapis.com"

  timeouts {
    create = "10m"
    update = "10m"
  }

  disable_on_destroy = false
}

resource "google_workflows_workflow" "main" {
  depends_on = [google_project_service.googleapis]

  name                = var.workflow_name
  region              = local.region
  description         = var.workflow_description
  service_account     = var.service_account_email
  call_log_level      = var.call_log_level
  labels              = var.labels
  user_env_vars       = var.env_vars
  source_contents     = join("", [for path in local.source_file_paths : file(path)])
  deletion_protection = var.deletion_protection
}

resource "google_cloud_scheduler_job" "scheduler_job" {
  count = var.schedule_cron != null ? 1 : 0

  name        = var.schedule_name != null ? var.schedule_name : "${google_workflows_workflow.main.name}-scheduler"
  project     = google_workflows_workflow.main.project
  region      = google_workflows_workflow.main.region
  description = "Triggers ${google_workflows_workflow.main.name}"
  schedule    = var.schedule_cron

  # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_scheduler_job#retry_config
  retry_config {
    retry_count          = var.schedule_retry_count
    min_backoff_duration = var.schedule_min_backoff_duration
    max_doublings        = var.schedule_max_doublings
  }

  http_target {
    http_method = "POST"
    uri         = "https://workflowexecutions.googleapis.com/v1/projects/${local.project}/locations/${local.region}/workflows/${var.workflow_name}/executions"

    # Workflow input need to be passed as an `argument`
    # https://cloud.google.com/workflows/docs/executing-workflow
    # https://stackoverflow.com/questions/********/create-google-cloud-workflow-with-cloud-scheduler-trigger-with-payload-using-ter
    body = base64encode(<<EOF
    {
      "argument": "${var.schedule_input}",
      "callLogLevel": "${var.call_log_level}"
    }
    EOF
    )
    headers = {
      "Content-Type" = "application/octet-stream"
    }
    oauth_token {
      service_account_email = var.service_account_email
    }
  }
}
