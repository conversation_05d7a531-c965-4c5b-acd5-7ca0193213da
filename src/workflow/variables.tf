variable "workflow_name" {
  type        = string
  description = "Workflow name"
}

variable "workflow_description" {
  type        = string
  description = "Description of the workflow provided by the user."
  default     = null
}

variable "service_account_email" {
  type        = string
  description = "Workflow service account email."
}

variable "call_log_level" {
  type        = string
  description = "The level of platform logging to apply to calls during executions of this workflow."
  default     = "LOG_ALL_CALLS"
}

variable "labels" {
  description = "A map of labels to apply to contained resources."
  type        = map(string)
  default     = {}
}

variable "env_vars" {
  description = "User-defined environment variables associated with this workflow revision."
  type        = map(string)
  default     = {}
}

variable "include_common" {
  type        = bool
  description = "Whether to include common.yaml (which included handy sub-workflows, like `run_http_function`)"
  default     = true
}

variable "source_file_paths" {
  type        = list(string)
  description = "The list of source file paths of the workflows. The total size limit is 128KB."
}

variable "deletion_protection" {
  type        = bool
  description = "Whether Terraform will be prevented from destroying the workflow."
  default     = false
}

variable "schedule_cron" {
  description = "CRON-like schedule, ref. https://cloud.google.com/scheduler/docs/configuring/cron-job-schedules"
  type        = string
  default     = null
}

variable "schedule_name" {
  description = "Cloud scheduler job name triggering the workflow. If not provided it will inherit the name from the workflow."
  type        = string
  default     = null
}

variable "schedule_retry_count" {
  description = "The number of retries using the exponential backoff procedure described by maxDoublings."
  type        = number
  default     = 3
}

variable "schedule_min_backoff_duration" {
  description = "The minimum amount of time to wait before retrying a job after it fails."
  type        = string
  default     = "3s"
}

variable "schedule_max_doublings" {
  description = "The time between retries will double maxDoublings times. A job's retry interval starts at minBackoffDuration, then doubles maxDoublings times, then increases linearly, and finally retries retries at intervals of maxBackoffDuration up to retryCount times."
  type        = number
  default     = 2
}

variable "schedule_input" {
  type        = string
  description = "HTTP request body send as input for the workflow. It will be json and base64 encoded."
  default     = "{ \\\"arg1\\\": \\\"value1\\\" }"
}
