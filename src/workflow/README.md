## GCP Workflows KB
- [Use the Workflows JSON schema in your IDE](https://cloud.google.com/workflows/docs/use-workflows-json-schema-with-ide)
- [Best practices](https://cloud.google.com/workflows/docs/best-practice)
- [Invoke Cloud Run functions or Cloud Run](https://cloud.google.com/workflows/docs/calling-run-functions)
- [Syntax cheat sheet](https://cloud.google.com/workflows/docs/reference/syntax/syntax-cheat-sheet)
- [Terraform resource](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/workflows_workflow)

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_cloud_scheduler_job.scheduler_job](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_scheduler_job) | resource |
| [google_project_service.googleapis](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_service) | resource |
| [google_workflows_workflow.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/workflows_workflow) | resource |
| [google_client_config.default](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/client_config) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_call_log_level"></a> [call\_log\_level](#input\_call\_log\_level) | The level of platform logging to apply to calls during executions of this workflow. | `string` | `"LOG_ALL_CALLS"` | no |
| <a name="input_deletion_protection"></a> [deletion\_protection](#input\_deletion\_protection) | Whether Terraform will be prevented from destroying the workflow. | `bool` | `false` | no |
| <a name="input_env_vars"></a> [env\_vars](#input\_env\_vars) | User-defined environment variables associated with this workflow revision. | `map(string)` | `{}` | no |
| <a name="input_include_common"></a> [include\_common](#input\_include\_common) | Whether to include common.yaml (which included handy sub-workflows, like `run_http_function`) | `bool` | `true` | no |
| <a name="input_labels"></a> [labels](#input\_labels) | A map of labels to apply to contained resources. | `map(string)` | `{}` | no |
| <a name="input_schedule_cron"></a> [schedule\_cron](#input\_schedule\_cron) | CRON-like schedule, ref. https://cloud.google.com/scheduler/docs/configuring/cron-job-schedules | `string` | `null` | no |
| <a name="input_schedule_input"></a> [schedule\_input](#input\_schedule\_input) | HTTP request body send as input for the workflow. It will be json and base64 encoded. | `string` | `"{ \\\"arg1\\\": \\\"value1\\\" }"` | no |
| <a name="input_schedule_max_doublings"></a> [schedule\_max\_doublings](#input\_schedule\_max\_doublings) | The time between retries will double maxDoublings times. A job's retry interval starts at minBackoffDuration, then doubles maxDoublings times, then increases linearly, and finally retries retries at intervals of maxBackoffDuration up to retryCount times. | `number` | `2` | no |
| <a name="input_schedule_min_backoff_duration"></a> [schedule\_min\_backoff\_duration](#input\_schedule\_min\_backoff\_duration) | The minimum amount of time to wait before retrying a job after it fails. | `string` | `"3s"` | no |
| <a name="input_schedule_name"></a> [schedule\_name](#input\_schedule\_name) | Cloud scheduler job name triggering the workflow. If not provided it will inherit the name from the workflow. | `string` | `null` | no |
| <a name="input_schedule_retry_count"></a> [schedule\_retry\_count](#input\_schedule\_retry\_count) | The number of retries using the exponential backoff procedure described by maxDoublings. | `number` | `3` | no |
| <a name="input_service_account_email"></a> [service\_account\_email](#input\_service\_account\_email) | Workflow service account email. | `string` | n/a | yes |
| <a name="input_source_file_paths"></a> [source\_file\_paths](#input\_source\_file\_paths) | The list of source file paths of the workflows. The total size limit is 128KB. | `list(string)` | n/a | yes |
| <a name="input_workflow_description"></a> [workflow\_description](#input\_workflow\_description) | Description of the workflow provided by the user. | `string` | `null` | no |
| <a name="input_workflow_name"></a> [workflow\_name](#input\_workflow\_name) | Workflow name | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_workflow_http_post_url"></a> [workflow\_http\_post\_url](#output\_workflow\_http\_post\_url) | Workflow http post URL |
| <a name="output_workflow_id"></a> [workflow\_id](#output\_workflow\_id) | Workflow id |
| <a name="output_workflow_landing_page_url"></a> [workflow\_landing\_page\_url](#output\_workflow\_landing\_page\_url) | Workflow landing page URL -- specifically executions page |
| <a name="output_workflow_name"></a> [workflow\_name](#output\_workflow\_name) | Workflow name |
| <a name="output_workflow_schedule_job_name"></a> [workflow\_schedule\_job\_name](#output\_workflow\_schedule\_job\_name) | Workflow cloud scheduler job name |
<!-- END_TF_DOCS -->
