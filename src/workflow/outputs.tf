output "workflow_id" {
  value       = google_workflows_workflow.main.id
  description = "Workflow id"
}

output "workflow_name" {
  value       = google_workflows_workflow.main.name
  description = "Workflow name"
}

output "workflow_landing_page_url" {
  value       = "https://console.cloud.google.com/workflows/workflow/${local.region}/${google_workflows_workflow.main.name}/executions?project=${local.project}"
  description = "Workflow landing page URL -- specifically executions page"
}

output "workflow_http_post_url" {
  value       = "https://workflowexecutions.googleapis.com/v1/${google_workflows_workflow.main.id}/executions"
  description = "Workflow http post URL"
}

output "workflow_schedule_job_name" {
  value       = one(google_cloud_scheduler_job.scheduler_job.*.name)
  description = "Workflow cloud scheduler job name"
}
