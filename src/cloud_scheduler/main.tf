locals {
  project_id = reverse(split("/", data.google_project.project.id))[0]

  function_subscribers = merge([
    for idx, sa_id in var.function_subscribers : {
      for role_id in ["roles/pubsub.subscriber", "roles/pubsub.viewer"] :
      "${idx}-${role_id}" => { sa = sa_id, role = role_id }
    }
  ]...)

  # Minimal permissions to schedule the job: https://cloud.google.com/run/docs/execute/jobs-on-schedule
  custom_role_permissions = ["cloudscheduler.jobs.create"]
  custom_role_id          = "cloudRunJobSchedulerRole${md5(jsonencode(local.custom_role_permissions))}"
  custom_role_full_name   = "projects/${local.project_id}/roles/${local.custom_role_id}"
}

data "google_project" "project" {
}

resource "google_pubsub_topic" "topic" {
  # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic
  name   = "${var.schedule_name}-scheduler-topic"
  labels = var.labels
}

# https://cloud.google.com/pubsub/docs/access-control
resource "google_pubsub_topic_iam_member" "topic_subscribers" {
  for_each = local.function_subscribers
  project  = google_pubsub_topic.topic.project
  topic    = google_pubsub_topic.topic.name
  role     = each.value["role"]
  member   = "serviceAccount:${each.value["sa"]}"
}

resource "google_cloud_scheduler_job" "function_job" {
  count = length(var.function_subscribers) > 0 ? 1 : 0

  # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_scheduler_job
  name      = "${var.schedule_name}-function"
  schedule  = var.cron
  time_zone = var.time_zone
  paused    = !var.enabled

  # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_scheduler_job#retry_config
  retry_config {
    min_backoff_duration = var.min_backoff_duration
    max_doublings        = var.max_doublings
    retry_count          = var.retry_count
  }

  pubsub_target {
    topic_name = google_pubsub_topic.topic.id
    data       = base64encode(var.pubsub_data)
  }
}

resource "google_cloud_scheduler_job" "cloud_run_job" {
  count = var.job_subscriber != null ? 1 : 0

  name      = "${var.schedule_name}-job"
  schedule  = var.cron
  time_zone = var.time_zone
  paused    = !var.enabled

  retry_config {
    min_backoff_duration = var.min_backoff_duration
    max_doublings        = var.max_doublings
    retry_count          = var.retry_count
  }

  http_target {
    # https://cloud.google.com/run/docs/execute/jobs-on-schedule
    http_method = "POST"
    uri         = "https://${var.gcp_region}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/${data.google_project.project.number}/jobs/${var.job_subscriber.job_name}:run"

    oauth_token {
      service_account_email = var.job_subscriber.sa_email
    }
  }
}

resource "google_project_iam_custom_role" "custom_permissions" {
  count       = var.job_subscriber != null ? 1 : 0
  role_id     = local.custom_role_id
  title       = local.custom_role_full_name
  permissions = local.custom_role_permissions
}

resource "google_project_iam_member" "predefined_roles" {
  depends_on = [google_project_iam_custom_role.custom_permissions]

  count   = var.job_subscriber != null ? 1 : 0
  role    = local.custom_role_full_name
  member  = "serviceAccount:${var.job_subscriber.sa_email}"
  project = local.project_id
}
