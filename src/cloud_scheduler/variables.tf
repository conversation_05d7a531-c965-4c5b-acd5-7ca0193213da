variable "gcp_region" {
  description = "GCP region"
  type        = string
  default     = "europe-west3"
}

variable "schedule_name" {
  description = "Name of the schedule"
  type        = string
}

variable "time_zone" {
  description = "The time zone name of the schedule, ref. https://cloud.google.com/scheduler/docs/configuring/cron-job-schedules#time_zone"
  type        = string
  default     = "UTC"
}

variable "cron" {
  description = "GCP CRON-like schedule, ref. https://cloud.google.com/scheduler/docs/configuring/cron-job-schedules"
  type        = string
}

variable "enabled" {
  description = "Sets the job to a enabled/disabled state, by default a job is running (means is not paused)."
  type        = bool
  default     = true
}

variable "retry_count" {
  description = "Number of reties in case of failure, ref. https://cloud.google.com/scheduler/docs/configuring/cron-job-schedules"
  type        = number
  default     = 3
}

variable "min_backoff_duration" {
  description = "The minimum amount of time to wait before retrying a job after it fails."
  type        = string
  default     = "3s"
}

variable "max_doublings" {
  description = "The time between retries will double min_backoff_duration"
  type        = number
  default     = 2
}

variable "pubsub_data" {
  description = "pubsub message"
  type        = string
  default     = "start"
}

variable "function_subscribers" {
  description = "List of service account emails for functions that should be triggered by this schedule, ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic_iam#google_pubsub_topic_iam_binding"
  type        = list(string)
  default     = []
}

variable "job_subscriber" {
  description = "Service account email and job name that should be triggered by this schedule, ref. https://cloud.google.com/run/docs/execute/jobs-on-schedule"
  type = object({
    job_name = string,
    sa_email = string,
  })
  default = null
}


variable "labels" {
  description = "A map of labels to apply to contained resources"
  type        = map(string)
  default     = {}
}
