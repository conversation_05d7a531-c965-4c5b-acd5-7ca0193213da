output "cloud_function_schedule_job_name" {
  description = "Cloud Function Schedule job name"

  value = one(google_cloud_scheduler_job.function_job.*.name)
}

output "cloud_run_job_schedule_job_name" {
  description = "Cloud Function Schedule job name"
  value       = one(google_cloud_scheduler_job.cloud_run_job.*.name)
}

output "schedule_topic_name" {
  description = "Pub/Sub topic name"
  value       = google_pubsub_topic.topic.name
}

output "schedule_topic_id" {
  description = "Pub/Sub topic id"
  value       = google_pubsub_topic.topic.id
}

output "function_schedule_cron" {
  description = "Function schedule cron"
  value       = one(google_cloud_scheduler_job.function_job.*.schedule)
}

output "function_schedule_time_zone" {
  description = "Function schedule time zone"
  value       = one(google_cloud_scheduler_job.function_job.*.time_zone)
}

output "job_schedule_cron" {
  description = "Job schedule cron"
  value       = one(google_cloud_scheduler_job.cloud_run_job.*.schedule)
}

output "job_schedule_time_zone" {
  description = "Job schedule time zone"
  value       = one(google_cloud_scheduler_job.cloud_run_job.*.time_zone)
}
