# Cloud Scheduler

A module to abstract Cloud Scheduler Pub/Sub job creation:
- Create Pub/Sub topic;
- (Optionally) Add service account topic subscribers;
- Create a Cloud Scheduler job with given retries and possibility to pause it.

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_cloud_scheduler_job.cloud_run_job](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_scheduler_job) | resource |
| [google_cloud_scheduler_job.function_job](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_scheduler_job) | resource |
| [google_project_iam_custom_role.custom_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_custom_role) | resource |
| [google_project_iam_member.predefined_roles](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_pubsub_topic.topic](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic) | resource |
| [google_pubsub_topic_iam_member.topic_subscribers](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic_iam_member) | resource |
| [google_project.project](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/project) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_cron"></a> [cron](#input\_cron) | GCP CRON-like schedule, ref. https://cloud.google.com/scheduler/docs/configuring/cron-job-schedules | `string` | n/a | yes |
| <a name="input_enabled"></a> [enabled](#input\_enabled) | Sets the job to a enabled/disabled state, by default a job is running (means is not paused). | `bool` | `true` | no |
| <a name="input_function_subscribers"></a> [function\_subscribers](#input\_function\_subscribers) | List of service account emails for functions that should be triggered by this schedule, ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic_iam#google_pubsub_topic_iam_binding | `list(string)` | `[]` | no |
| <a name="input_gcp_region"></a> [gcp\_region](#input\_gcp\_region) | GCP region | `string` | `"europe-west3"` | no |
| <a name="input_job_subscriber"></a> [job\_subscriber](#input\_job\_subscriber) | Service account email and job name that should be triggered by this schedule, ref. https://cloud.google.com/run/docs/execute/jobs-on-schedule | <pre>object({<br/>    job_name = string,<br/>    sa_email = string,<br/>  })</pre> | `null` | no |
| <a name="input_labels"></a> [labels](#input\_labels) | A map of labels to apply to contained resources | `map(string)` | `{}` | no |
| <a name="input_max_doublings"></a> [max\_doublings](#input\_max\_doublings) | The time between retries will double min\_backoff\_duration | `number` | `2` | no |
| <a name="input_min_backoff_duration"></a> [min\_backoff\_duration](#input\_min\_backoff\_duration) | The minimum amount of time to wait before retrying a job after it fails. | `string` | `"3s"` | no |
| <a name="input_pubsub_data"></a> [pubsub\_data](#input\_pubsub\_data) | pubsub message | `string` | `"start"` | no |
| <a name="input_retry_count"></a> [retry\_count](#input\_retry\_count) | Number of reties in case of failure, ref. https://cloud.google.com/scheduler/docs/configuring/cron-job-schedules | `number` | `3` | no |
| <a name="input_schedule_name"></a> [schedule\_name](#input\_schedule\_name) | Name of the schedule | `string` | n/a | yes |
| <a name="input_time_zone"></a> [time\_zone](#input\_time\_zone) | The time zone name of the schedule, ref. https://cloud.google.com/scheduler/docs/configuring/cron-job-schedules#time_zone | `string` | `"UTC"` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_cloud_function_schedule_job_name"></a> [cloud\_function\_schedule\_job\_name](#output\_cloud\_function\_schedule\_job\_name) | Cloud Function Schedule job name |
| <a name="output_cloud_run_job_schedule_job_name"></a> [cloud\_run\_job\_schedule\_job\_name](#output\_cloud\_run\_job\_schedule\_job\_name) | Cloud Function Schedule job name |
| <a name="output_function_schedule_cron"></a> [function\_schedule\_cron](#output\_function\_schedule\_cron) | Function schedule cron |
| <a name="output_function_schedule_time_zone"></a> [function\_schedule\_time\_zone](#output\_function\_schedule\_time\_zone) | Function schedule time zone |
| <a name="output_job_schedule_cron"></a> [job\_schedule\_cron](#output\_job\_schedule\_cron) | Job schedule cron |
| <a name="output_job_schedule_time_zone"></a> [job\_schedule\_time\_zone](#output\_job\_schedule\_time\_zone) | Job schedule time zone |
| <a name="output_schedule_topic_id"></a> [schedule\_topic\_id](#output\_schedule\_topic\_id) | Pub/Sub topic id |
| <a name="output_schedule_topic_name"></a> [schedule\_topic\_name](#output\_schedule\_topic\_name) | Pub/Sub topic name |
<!-- END_TF_DOCS -->
