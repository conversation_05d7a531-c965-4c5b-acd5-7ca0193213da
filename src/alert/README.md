# Alert

GCP Alerting Module

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_logging_metric.logging_metric](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/logging_metric) | resource |
| [google_monitoring_alert_policy.logging_metric_policy](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_alert_policy) | resource |
| [google_monitoring_notification_channel.email](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_notification_channel) | resource |
| [google_monitoring_notification_channel.pubsub](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_notification_channel) | resource |
| [google_pubsub_topic_iam_member.monitoring_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic_iam_member) | resource |
| [google_project.project](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/project) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_alert_name"></a> [alert\_name](#input\_alert\_name) | Optional alert name | `string` | `null` | no |
| <a name="input_emails"></a> [emails](#input\_emails) | Notification e-mail addresses | `set(string)` | `[]` | no |
| <a name="input_incident_autoclose_duration"></a> [incident\_autoclose\_duration](#input\_incident\_autoclose\_duration) | Incident autoclose duration in seconds, ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_alert_policy#auto_close | `string` | `"1800s"` | no |
| <a name="input_resource_type"></a> [resource\_type](#input\_resource\_type) | Logging resource type, ref. https://cloud.google.com/monitoring/api/resources | `string` | n/a | yes |
| <a name="input_rolling_window"></a> [rolling\_window](#input\_rolling\_window) | The length of time in seconds that a signal is calculated for, ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_alert_policy#alignment_period | `string` | `"60s"` | no |
| <a name="input_severity"></a> [severity](#input\_severity) | The severity of the event in a log entry, ref. https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry#logseverity | `string` | n/a | yes |
| <a name="input_should_create"></a> [should\_create](#input\_should\_create) | Allows to create all necessary resources in a module. To be used for conditional creation of a module | `bool` | `true` | no |
| <a name="input_topic_ids"></a> [topic\_ids](#input\_topic\_ids) | Notification PubSub topic ids | `set(string)` | `[]` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_logging_metric_name"></a> [logging\_metric\_name](#output\_logging\_metric\_name) | Logging metric name |
<!-- END_TF_DOCS -->
