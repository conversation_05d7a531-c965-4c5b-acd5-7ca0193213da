variable "alert_name" {
  description = "Optional alert name"
  type        = string
  default     = null
}

variable "resource_type" {
  description = "Logging resource type, ref. https://cloud.google.com/monitoring/api/resources"
  type        = string
}

variable "severity" {
  description = "The severity of the event in a log entry, ref. https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry#logseverity"
  type        = string
}

variable "emails" {
  description = "Notification e-mail addresses"
  type        = set(string)
  default     = []
}

variable "topic_ids" {
  description = "Notification PubSub topic ids"
  type        = set(string)
  default     = []
}

variable "rolling_window" {
  description = "The length of time in seconds that a signal is calculated for, ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_alert_policy#alignment_period"
  type        = string
  default     = "60s"
}

variable "incident_autoclose_duration" {
  description = "Incident autoclose duration in seconds, ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_alert_policy#auto_close"
  type        = string
  default     = "1800s"
}

variable "should_create" {
  description = "Allows to create all necessary resources in a module. To be used for conditional creation of a module"
  type        = bool
  default     = true
}
