locals {
  email_count         = length(var.emails)
  topic_count         = length(var.topic_ids)
  email_should_create = local.email_count > 0
  topic_should_create = local.topic_count > 0
  should_create       = var.should_create ? local.email_should_create || local.topic_should_create : false
  alert_name          = var.alert_name != null ? "${var.alert_name}-" : ""
  metric_name         = "${local.alert_name}${replace(var.resource_type, "_", "-")}-${lower(var.severity)}"
  filter_query        = "resource.type=\"${var.resource_type}\" AND severity=\"${var.severity}\""
  severity            = upper(var.severity)

  emails = { for index in range(local.email_count) : index => tolist(var.emails)[index] }
  topics = { for index in range(local.topic_count) : index => tolist(var.topic_ids)[index] }

  project_id = reverse(split("/", data.google_project.project.id))[0]
}

data "google_project" "project" {
}


resource "google_logging_metric" "logging_metric" {
  name   = local.metric_name
  filter = local.filter_query
  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
    unit        = "1"
  }
}

resource "google_monitoring_notification_channel" "email" {
  for_each     = local.emails
  display_name = "${local.metric_name}-${each.value}-email-channel"
  type         = "email"
  labels = {
    email_address = each.value
  }
  force_delete = true
}

resource "google_monitoring_notification_channel" "pubsub" {
  for_each     = local.topics
  display_name = "${local.metric_name}-${each.value}-pubsub-channel"
  type         = "pubsub"

  labels = {
    topic = each.value
  }
  force_delete = true
}

resource "google_monitoring_alert_policy" "logging_metric_policy" {
  count = local.should_create ? 1 : 0

  display_name = "${local.metric_name}-alert"
  combiner     = "OR"
  severity     = local.severity

  conditions {
    display_name = "${local.metric_name}-alert-condition"
    condition_threshold {
      filter = "resource.type=\"${var.resource_type}\" AND metric.type=\"logging.googleapis.com/user/${google_logging_metric.logging_metric.name}\""
      aggregations {
        alignment_period     = var.rolling_window
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_COUNT"
      }
      comparison = "COMPARISON_GT"
      duration   = "0s"
      trigger {
        count = 1
      }
    }
  }

  alert_strategy {
    auto_close           = var.incident_autoclose_duration
    notification_prompts = ["OPENED"]
  }

  notification_channels = flatten([
    [for o in google_monitoring_notification_channel.email : o.name],
    [for o in google_monitoring_notification_channel.pubsub : o.name]
  ])
}

# https://cloud.google.com/monitoring/support/notification-options?_gl=1*1egtt0i*_ga******************************_ga_WH2QY8WWF5***********************************************..#pubsub
resource "google_pubsub_topic_iam_member" "monitoring_permissions" {
  for_each = local.topics
  role     = "roles/pubsub.publisher"
  member   = "serviceAccount:service-${data.google_project.project.number}@gcp-sa-monitoring-notification.iam.gserviceaccount.com"
  project  = local.project_id
  topic    = each.value
}
