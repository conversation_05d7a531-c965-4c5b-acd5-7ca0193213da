resource "google_secret_manager_secret" "this" {
  # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret

  secret_id = var.secret_id
  labels    = var.labels

  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "this_value" {
  count = var.secret_value != null ? 1 : 0

  secret      = google_secret_manager_secret.this.id
  secret_data = var.secret_value

  lifecycle {
    ignore_changes = [secret_data]
  }
}

resource "google_secret_manager_secret_iam_binding" "secret_reader_binding" {
  # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret_iam

  secret_id = google_secret_manager_secret.this.secret_id
  role      = "roles/secretmanager.secretAccessor"
  members   = [for sa in var.sa_readers : "serviceAccount:${sa}"]
}
