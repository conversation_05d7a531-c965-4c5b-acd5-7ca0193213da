variable "secret_id" {
  description = "Secret id: must be unique within the project"
  type        = string
}

variable "secret_value" {
  description = "Secret value"
  type        = string
  default     = null
}

variable "labels" {
  description = "A map of labels to apply to contained resources"
  type        = map(string)
  default     = {}
}

variable "sa_readers" {
  # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret_iam#member/members
  description = "List of service account emails that will be granted the reader role."
  type        = list(string)
  default     = []
}
