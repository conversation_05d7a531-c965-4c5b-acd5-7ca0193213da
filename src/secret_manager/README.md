# Secret manager

A module to abstract secret manager creation:
- Create secret with default replication policy;
- (Optionally) Update the IAM policy for the secret to grant a role to a list of members;

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_secret_manager_secret.this](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret) | resource |
| [google_secret_manager_secret_iam_binding.secret_reader_binding](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret_iam_binding) | resource |
| [google_secret_manager_secret_version.this_value](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret_version) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_labels"></a> [labels](#input\_labels) | A map of labels to apply to contained resources | `map(string)` | `{}` | no |
| <a name="input_sa_readers"></a> [sa\_readers](#input\_sa\_readers) | List of service account emails that will be granted the reader role. | `list(string)` | `[]` | no |
| <a name="input_secret_id"></a> [secret\_id](#input\_secret\_id) | Secret id: must be unique within the project | `string` | n/a | yes |
| <a name="input_secret_value"></a> [secret\_value](#input\_secret\_value) | Secret value | `string` | `null` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_secret_id"></a> [secret\_id](#output\_secret\_id) | Secret id, example: dhl-sftp |
| <a name="output_secret_name"></a> [secret\_name](#output\_secret\_name) | Fully qualified secret name, example: projects/************/secrets/dhl-sftp |
<!-- END_TF_DOCS -->
