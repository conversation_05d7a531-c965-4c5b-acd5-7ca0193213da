variable "sa_name" {
  description = "Service account name"
  type        = string
}

variable "should_create_service_account" {
  description = "Whether to create `sa_name`. If not, `sa_name` must be fully qualified"
  type        = bool
  default     = true
}

variable "predefined_roles" {
  description = <<EOF
  List of predefined roles with optional resource name.
  If a resource name is not provided, the permissions are granted at the project level.
  The default approach should be to specify a resource name whenever possible.
  Ref. https://cloud.google.com/iam/docs/understanding-roles#predefined
  For resource names, see https://cloud.google.com/iam/docs/conditions-resource-attributes#resource-name
  EOF
  type = list(object({
    roles          = list(string),
    resource_names = optional(list(string), []),
  }))
  default = []
}

variable "custom_permissions" {
  description = <<EOF
     List of GCP custom permissions to be applied to the service account.
     If a resource name is not provided, the permissions are granted at the project level.
     The default approach should be to specify a resource name whenever possible.
     Ref. https://cloud.google.com/iam/docs/permissions-reference
     For resource name follow the https://cloud.google.com/iam/docs/conditions-resource-attributes#resource-name
  EOF
  type = list(object({
    permissions    = list(string),
    resource_names = optional(list(string), []),
  }))
  default = []
}

variable "should_create" {
  description = "Allows to create all necessary resources in a module. To be used for conditional creation of a module"
  type        = bool
  default     = true
}
