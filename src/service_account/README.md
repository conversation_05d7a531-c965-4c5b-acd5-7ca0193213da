# Service account

A module to abstract service account creation:
- Create service account;
- (Optionally) Add predefined roles bindings;
- (Optionally) Add custom permissions bindings.

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_project_iam_custom_role.custom_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_custom_role) | resource |
| [google_project_iam_member.role_binding](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_service_account.sa](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_client_config.config](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/client_config) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_custom_permissions"></a> [custom\_permissions](#input\_custom\_permissions) | List of GCP custom permissions to be applied to the service account.<br/>     If a resource name is not provided, the permissions are granted at the project level.<br/>     The default approach should be to specify a resource name whenever possible.<br/>     Ref. https://cloud.google.com/iam/docs/permissions-reference<br/>     For resource name follow the https://cloud.google.com/iam/docs/conditions-resource-attributes#resource-name | <pre>list(object({<br/>    permissions    = list(string),<br/>    resource_names = optional(list(string), []),<br/>  }))</pre> | `[]` | no |
| <a name="input_predefined_roles"></a> [predefined\_roles](#input\_predefined\_roles) | List of predefined roles with optional resource name.<br/>  If a resource name is not provided, the permissions are granted at the project level.<br/>  The default approach should be to specify a resource name whenever possible.<br/>  Ref. https://cloud.google.com/iam/docs/understanding-roles#predefined<br/>  For resource names, see https://cloud.google.com/iam/docs/conditions-resource-attributes#resource-name | <pre>list(object({<br/>    roles          = list(string),<br/>    resource_names = optional(list(string), []),<br/>  }))</pre> | `[]` | no |
| <a name="input_sa_name"></a> [sa\_name](#input\_sa\_name) | Service account name | `string` | n/a | yes |
| <a name="input_should_create"></a> [should\_create](#input\_should\_create) | Allows to create all necessary resources in a module. To be used for conditional creation of a module | `bool` | `true` | no |
| <a name="input_should_create_service_account"></a> [should\_create\_service\_account](#input\_should\_create\_service\_account) | Whether to create `sa_name`. If not, `sa_name` must be fully qualified | `bool` | `true` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_account_id"></a> [account\_id](#output\_account\_id) | Used to generate the service account email address and a stable unique id |
| <a name="output_sa_email"></a> [sa\_email](#output\_sa\_email) | Service account email |
<!-- END_TF_DOCS -->
