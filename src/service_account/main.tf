locals {
  project_id       = data.google_client_config.config.project
  sa_name          = replace(var.sa_name, ".iam.gserviceaccount.com", "")
  custom_role_name = "${replace(local.sa_name, "/[@./]/", "-")}-custom-role"
  custom_permissions = {
    for id, perm in var.custom_permissions : id => {
      permissions           = perm.permissions,
      resource_names        = perm.resource_names,
      custom_role_id        = "${md5(local.custom_role_name)}${md5(jsonencode(perm.permissions))}"
      custom_role_full_name = "projects/${local.project_id}/roles/${md5(local.custom_role_name)}${md5(jsonencode(perm.permissions))}"
    }
  }

  custom_roles = merge([
    for id, perm in local.custom_permissions : {
      for resource_id, resource_name in length(perm["resource_names"]) == 0 ? concat(perm["resource_names"], [null]) : perm["resource_names"] :
      "custom_role_${coalesce(resource_id, 0)}_${id}" => {
        role          = perm["custom_role_full_name"],
        resource_name = resource_name
      }
    }
  ]...)

  predefined_roles = merge([
    for id, roles in var.predefined_roles : merge([
      for role_id, role in roles.roles : {
        for resource_id, resource_name in length(roles["resource_names"]) == 0 ? concat(roles["resource_names"], [null]) : roles["resource_names"] :
        "predefined_role_${coalesce(resource_id, 0)}_${role_id}_${id}" => {
          role          = role,
          resource_name = resource_name
        }
      }
    ]...)
  ]...)

  sa_roles     = var.should_create ? merge(local.custom_roles, local.predefined_roles) : {}
  sa_full_name = var.should_create_service_account ? one(google_service_account.sa.*.email) : var.sa_name
}

data "google_client_config" "config" {}

resource "google_service_account" "sa" {
  count = var.should_create && var.should_create_service_account ? 1 : 0

  account_id   = var.sa_name
  display_name = var.sa_name
}

resource "google_project_iam_custom_role" "custom_permissions" {
  for_each = local.custom_permissions

  role_id     = each.value["custom_role_id"]
  title       = local.custom_role_name
  permissions = each.value["permissions"]
}

resource "google_project_iam_member" "role_binding" {
  depends_on = [google_project_iam_custom_role.custom_permissions]

  for_each = local.sa_roles
  role     = each.value["role"]
  member   = "serviceAccount:${local.sa_full_name}"
  project  = local.project_id

  dynamic "condition" {
    for_each = each.value["resource_name"] != null ? [each.value["resource_name"]] : []
    iterator = resource
    content {
      expression = "resource.name.startsWith('${resource.value}')"
      title      = "Access limited to the '${resource.value}'"
    }
  }
}
