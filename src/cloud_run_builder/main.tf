# Common module for building and pushing Docker images for Cloud Run services and jobs
# This module handles the shared functionality between cloud_run_job and cloud_run_function

locals {
  runtime_docker_image       = var.runtime_docker_image
  gcp_project_id             = data.google_client_config.config.project
  gcp_region                 = data.google_client_config.config.region
  infra_dir                  = abspath(path.cwd)
  tmp_root_dir               = "${local.infra_dir}/.temp/builds/${var.build_context}"
  tmp_dir                    = "${local.tmp_root_dir}/${var.service_name}"
  tmp_dir_resources          = "${local.tmp_dir}/resources/"
  is_common_included         = var.common_path != null
  is_shared_included         = var.shared_path != null
  is_non_default_docker_file = var.docker_file_path != null
  is_resources_path_included = var.resources_path != null
  code_path                  = abspath(var.code_root_path)
  shared_path                = local.is_shared_included ? abspath(var.shared_path) : ""
  common_path                = local.is_common_included ? abspath(var.common_path) : ""
  resources_path             = local.is_resources_path_included ? abspath(var.resources_path) : ""
  docker_file_path           = local.is_non_default_docker_file ? abspath(var.docker_file_path) : var.default_docker_file_path
  fileset_pattern            = "**"
  file_to_exclude            = "__pycache__"

  image_name     = "${var.artifact_registry_url}/${var.service_name}"
  image_name_tag = "${local.image_name}:${random_id.image_tag_suffix.dec}"

  run_on_change = { change = var.force_deploy ? uuid() : random_uuid.code_source_hash.result }
}

data "google_client_config" "config" {}

resource "random_uuid" "code_source_hash" {
  keepers = {
    for file_path in setunion(
      [for name in fileset(var.code_root_path, local.fileset_pattern) : "${var.code_root_path}/${name}"],
      local.is_common_included ?
      [for name in fileset(var.common_path, local.fileset_pattern) : "${var.common_path}/${name}"] : [],
      local.is_shared_included ?
      [for name in fileset(var.shared_path, local.fileset_pattern) : "${var.shared_path}/${name}"] : [],
      local.is_resources_path_included ?
      [for name in fileset(var.resources_path, local.fileset_pattern) : "${var.resources_path}/${name}"] : [],
    ) :
    file_path => filemd5(file_path) if strcontains(file_path, local.file_to_exclude) == false
  }
}

resource "null_resource" "prepare_dependencies" {
  depends_on = [random_uuid.code_source_hash]

  provisioner "local-exec" {
    interpreter = ["bash", "-c"]
    command     = <<CMD
      set -euo pipefail
      rm -rf ${local.tmp_dir}
      mkdir -p ${local.tmp_dir}

      echo "Copying '${local.docker_file_path}' file..."
      cp -a ${local.docker_file_path} ${local.tmp_dir}

      echo "Copying '${local.code_path}' ${var.service_type} dependencies..."
      cp -a ${local.code_path}/. ${local.tmp_dir}

      if [ -n "${local.shared_path}" ]; then
        echo "Copying '${local.shared_path}' ${var.service_type} shared dependencies..."
        cp -a ${local.shared_path} ${local.tmp_dir}
      fi

      if [ -n "${local.common_path}" ]; then
        echo "Copying '${local.common_path}' ${var.service_type} common dependencies..."
        cp -a ${local.common_path} ${local.tmp_dir}
      fi

      if [ -n "${local.resources_path}" ]; then
        echo "Copying '${local.resources_path}' ${var.service_type} extra resources..."
        cp -a ${local.resources_path} ${local.tmp_dir_resources}
      fi

    CMD
  }

  triggers = local.run_on_change
}

resource "random_id" "image_tag_suffix" {
  depends_on  = [random_uuid.code_source_hash]
  byte_length = 4
  keepers     = local.run_on_change
}

resource "null_resource" "build_push_docker_image" {
  depends_on = [null_resource.prepare_dependencies]

  provisioner "local-exec" {
    interpreter = ["bash", "-c"]
    command     = <<CMD
      set -euo pipefail
      cd ${local.tmp_dir}
      echo "Building Docker image with '${local.image_name_tag}' tag..."
      docker build -q -t ${local.image_name_tag} --build-arg IMAGE_VERSION=${local.runtime_docker_image} .
      echo "Authenticate to GCP Artifact Registry..."
      gcloud auth configure-docker ${local.gcp_region}-docker.pkg.dev --quiet
      echo "Pushing Docker image with '${local.image_name_tag}' tag..."
      docker push ${local.image_name_tag}
    CMD
  }

  triggers = local.run_on_change
}
