# Cloud Run Builder

A common module that handles Docker image building and deployment for Cloud Run services and jobs. 

This module extracts the shared functionality between `cloud_run_job` and `cloud_run_function` modules, following the DRY (Don't Repeat Yourself) principle.

## Features

- **Docker Image Building**: Builds and pushes Docker images to Artifact Registry
- **Dependency Management**: Handles shared, common, and resource dependencies
- **Change Detection**: Automatically detects code changes and rebuilds only when necessary
- **Path Management**: Manages temporary directories and file paths
- **Flexible Configuration**: Supports custom Docker files and runtime images

## Usage

This module is typically used as a dependency by other modules like `cloud_run_job` and `cloud_run_function`:

```hcl
module "builder" {
  source = "../cloud_run_builder"

  service_name          = "my-service"
  service_type          = "function"
  build_context         = "cloud_run_function"
  artifact_registry_url = "europe-west3-docker.pkg.dev/my-project/my-repo"
  code_root_path        = "./src/my_function"
  default_docker_file_path = "${path.module}/Dockerfile"

  shared_path   = "./shared"
  common_path   = "../src/common"
  force_deploy  = false
}
```

## Key Benefits

1. **DRY Principle**: Eliminates code duplication between Cloud Run modules
2. **Consistency**: Ensures consistent build processes across all Cloud Run services
3. **Maintainability**: Changes to build logic only need to be made in one place
4. **Reusability**: Can be used by any Cloud Run-based module

## Build Process

1. **Change Detection**: Calculates hash of all source files to detect changes
2. **Dependency Preparation**: Copies source code, shared dependencies, and resources
3. **Docker Build**: Builds Docker image with specified runtime
4. **Registry Push**: Authenticates and pushes image to Artifact Registry
5. **Tag Generation**: Generates unique tags based on content hash

## File Structure

The module expects the following structure:
```
code_root_path/
├── main.py (or your main code)
├── requirements.txt
└── ... (other source files)

shared_path/ (optional)
├── shared_module.py
└── ... (shared dependencies)

common_path/ (optional)
├── common_utils.py
└── ... (common dependencies)

resources_path/ (optional)
├── config.json
└── ... (extra resources)
```

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |
| <a name="provider_null"></a> [null](#provider\_null) | n/a |
| <a name="provider_random"></a> [random](#provider\_random) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [null_resource.build_push_docker_image](https://registry.terraform.io/providers/hashicorp/null/latest/docs/resources/resource) | resource |
| [null_resource.prepare_dependencies](https://registry.terraform.io/providers/hashicorp/null/latest/docs/resources/resource) | resource |
| [random_id.image_tag_suffix](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/id) | resource |
| [random_uuid.code_source_hash](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/uuid) | resource |
| [google_client_config.config](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/client_config) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_artifact_registry_url"></a> [artifact\_registry\_url](#input\_artifact\_registry\_url) | GCP Artifact Registry URL | `string` | n/a | yes |
| <a name="input_build_context"></a> [build\_context](#input\_build\_context) | Build context name (cloud\_run\_job, cloud\_run\_function, etc.) | `string` | n/a | yes |
| <a name="input_code_root_path"></a> [code\_root\_path](#input\_code\_root\_path) | Root directory path for the service code | `string` | n/a | yes |
| <a name="input_common_path"></a> [common\_path](#input\_common\_path) | Common dependencies directory path | `string` | `"../src/common"` | no |
| <a name="input_default_docker_file_path"></a> [default\_docker\_file\_path](#input\_default\_docker\_file\_path) | Default Docker file path when docker\_file\_path is null | `string` | n/a | yes |
| <a name="input_docker_file_path"></a> [docker\_file\_path](#input\_docker\_file\_path) | Docker file path, null means use default | `string` | `null` | no |
| <a name="input_force_deploy"></a> [force\_deploy](#input\_force\_deploy) | Allows to redeploy the code even if it has not been changed | `bool` | `false` | no |
| <a name="input_resources_path"></a> [resources\_path](#input\_resources\_path) | Extra resources directory path | `string` | `null` | no |
| <a name="input_runtime_docker_image"></a> [runtime\_docker\_image](#input\_runtime\_docker\_image) | Base Docker image for the runtime | `string` | `"python:3.12-slim"` | no |
| <a name="input_service_name"></a> [service\_name](#input\_service\_name) | Name of the service (job or function) | `string` | n/a | yes |
| <a name="input_service_type"></a> [service\_type](#input\_service\_type) | Type of service (job, function, etc.) - used for logging | `string` | `"service"` | no |
| <a name="input_shared_path"></a> [shared\_path](#input\_shared\_path) | Shared dependencies directory path | `string` | `null` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_build_triggers"></a> [build\_triggers](#output\_build\_triggers) | Build triggers for dependency tracking |
| <a name="output_gcp_project_id"></a> [gcp\_project\_id](#output\_gcp\_project\_id) | GCP Project ID |
| <a name="output_gcp_region"></a> [gcp\_region](#output\_gcp\_region) | GCP Region |
| <a name="output_image_name"></a> [image\_name](#output\_image\_name) | Docker image name without tag |
| <a name="output_image_name_tag"></a> [image\_name\_tag](#output\_image\_name\_tag) | Full Docker image name with tag |
| <a name="output_image_tag"></a> [image\_tag](#output\_image\_tag) | Generated image tag |
<!-- END_TF_DOCS -->
