variable "service_name" {
  description = "Name of the service (job or function)"
  type        = string
}

variable "service_type" {
  description = "Type of service (job, function, etc.) - used for logging"
  type        = string
  default     = "service"
}

variable "build_context" {
  description = "Build context name (cloud_run_job, cloud_run_function, etc.)"
  type        = string
}

variable "artifact_registry_url" {
  description = "GCP Artifact Registry URL"
  type        = string
}

variable "code_root_path" {
  description = "Root directory path for the service code"
  type        = string
}

variable "shared_path" {
  description = "Shared dependencies directory path"
  type        = string
  default     = null
}

variable "common_path" {
  description = "Common dependencies directory path"
  type        = string
  default     = "../src/common"
}

variable "resources_path" {
  description = "Extra resources directory path"
  type        = string
  default     = null
}

variable "docker_file_path" {
  description = "Docker file path, null means use default"
  type        = string
  default     = null
}

variable "default_docker_file_path" {
  description = "Default Docker file path when docker_file_path is null"
  type        = string
}

variable "runtime_docker_image" {
  description = "Base Docker image for the runtime"
  type        = string
  default     = "python:3.12-slim"
}

variable "force_deploy" {
  description = "Allows to redeploy the code even if it has not been changed"
  type        = bool
  default     = false
}
