output "image_name_tag" {
  value       = local.image_name_tag
  description = "Full Docker image name with tag"
}

output "image_name" {
  value       = local.image_name
  description = "Docker image name without tag"
}

output "image_tag" {
  value       = random_id.image_tag_suffix.dec
  description = "Generated image tag"
}

output "build_triggers" {
  value       = local.run_on_change
  description = "Build triggers for dependency tracking"
}

output "gcp_project_id" {
  value       = local.gcp_project_id
  description = "GCP Project ID"
}

output "gcp_region" {
  value       = local.gcp_region
  description = "GCP Region"
}
