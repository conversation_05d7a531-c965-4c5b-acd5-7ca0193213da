locals {
  # to overcome issue: Field 'olderThan', Illegal duration format; duration must end with 's'
  policy_delete_older_than_seconds = var.delete_artifact_older_than != null ? var.delete_artifact_older_than * 24 * 60 * 60 : 0
}

data "google_client_config" "config" {}

resource "google_artifact_registry_repository" "this" {
  count = var.should_create ? 1 : 0

  repository_id = var.repository_name
  format        = var.repository_type

  # https://cloud.google.com/artifact-registry/docs/repositories/cleanup-policy
  dynamic "cleanup_policies" {
    for_each = var.delete_artifact_older_than != null ? [local.policy_delete_older_than_seconds] : []

    content {
      id     = "delete-older-than-${var.delete_artifact_older_than}-days"
      action = "DELETE"
      condition {
        older_than = "${local.policy_delete_older_than_seconds}s"
      }
    }
  }

  dynamic "cleanup_policies" {
    for_each = var.keep_artifact_count != null ? [var.keep_artifact_count] : []

    content {
      id     = "keep-minimum-${var.keep_artifact_count}-versions"
      action = "KEEP"
      most_recent_versions {
        keep_count = var.keep_artifact_count
      }
    }
  }
  labels = var.labels
}
