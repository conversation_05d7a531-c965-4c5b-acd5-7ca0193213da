variable "repository_name" {
  description = "Name of the repository"
  type        = string
}

variable "repository_type" {
  description = "Type of the repository"
  type        = string
  default     = "DOCKER"
}

variable "delete_artifact_older_than" {
  description = "Delete older than days artifact, null means keep all artifacts"
  type        = number
  default     = null
}

variable "keep_artifact_count" {
  description = "Keep minimum number of artifacts, null means keep all artifacts"
  type        = number
  default     = null
}

variable "should_create" {
  description = "Allows to create all necessary resources in a module. To be used for conditional creation of a module"
  type        = bool
  default     = true
}

variable "labels" {
  description = "A map of labels to apply to contained resources"
  type        = map(string)
  default     = {}
}
