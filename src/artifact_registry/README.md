# Artifact Registry

Artifact Registry module

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_artifact_registry_repository.this](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/artifact_registry_repository) | resource |
| [google_client_config.config](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/client_config) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_delete_artifact_older_than"></a> [delete\_artifact\_older\_than](#input\_delete\_artifact\_older\_than) | Delete older than days artifact, null means keep all artifacts | `number` | `null` | no |
| <a name="input_keep_artifact_count"></a> [keep\_artifact\_count](#input\_keep\_artifact\_count) | Keep minimum number of artifacts, null means keep all artifacts | `number` | `null` | no |
| <a name="input_labels"></a> [labels](#input\_labels) | A map of labels to apply to contained resources | `map(string)` | `{}` | no |
| <a name="input_repository_name"></a> [repository\_name](#input\_repository\_name) | Name of the repository | `string` | n/a | yes |
| <a name="input_repository_type"></a> [repository\_type](#input\_repository\_type) | Type of the repository | `string` | `"DOCKER"` | no |
| <a name="input_should_create"></a> [should\_create](#input\_should\_create) | Allows to create all necessary resources in a module. To be used for conditional creation of a module | `bool` | `true` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_repository_url"></a> [repository\_url](#output\_repository\_url) | Repository URL |
<!-- END_TF_DOCS -->
