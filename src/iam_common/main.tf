locals {
  project_id = data.google_client_config.config.project

  # Process custom permissions
  custom_permissions = {
    for id, perm in var.custom_permissions : id => {
      permissions           = perm.permissions,
      resource_names        = perm.resource_names,
      custom_role_id        = "${md5(var.custom_role_name)}${md5(jsonencode(perm.permissions))}"
      custom_role_full_name = "projects/${local.project_id}/roles/${md5(var.custom_role_name)}${md5(jsonencode(perm.permissions))}"
    }
  }

  # Map custom roles to resources
  custom_roles = merge([
    for id, perm in local.custom_permissions : {
      for resource_id, resource_name in length(perm["resource_names"]) == 0 ? concat(perm["resource_names"], [null]) : perm["resource_names"] :
      "custom_role_${coalesce(resource_id, 0)}_${id}" => {
        role          = perm["custom_role_full_name"],
        resource_name = resource_name
      }
    }
  ]...)

  # Map predefined roles to resources
  predefined_roles = merge([
    for id, roles in var.predefined_roles : merge([
      for role_id, role in roles.roles : {
        for resource_id, resource_name in length(roles["resource_names"]) == 0 ? concat(roles["resource_names"], [null]) : roles["resource_names"] :
        "predefined_role_${coalesce(resource_id, 0)}_${role_id}_${id}" => {
          role          = role,
          resource_name = resource_name
        }
      }
    ]...)
  ]...)

  # Merge all roles
  all_roles = var.should_create ? merge(local.custom_roles, local.predefined_roles) : {}
}

data "google_client_config" "config" {}

resource "google_project_iam_custom_role" "custom_roles" {
  for_each = local.custom_permissions

  role_id     = each.value["custom_role_id"]
  title       = var.custom_role_name
  permissions = each.value["permissions"]
}

resource "google_project_iam_member" "role_bindings" {
  depends_on = [google_project_iam_custom_role.custom_roles]

  for_each = local.all_roles
  role     = each.value["role"]
  member   = var.member
  project  = local.project_id

  dynamic "condition" {
    for_each = each.value["resource_name"] != null ? [each.value["resource_name"]] : []
    iterator = resource
    content {
      expression = "resource.name.startsWith('${resource.value}')"
      title      = "Access limited to the '${resource.value}'"
    }
  }
}
