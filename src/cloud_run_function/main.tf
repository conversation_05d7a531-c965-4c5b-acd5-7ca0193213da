# GCP ref. https://cloud.google.com/run/docs/reference/rest/v2/projects.locations.services
# Terraform ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_v2_service

locals {
  module_dir = abspath(path.module)

  # Cloud Function Gen2 specific environment variables
  function_env_vars = merge(
    var.env_variables != null ? var.env_variables : {},
    {
      FUNCTION_TARGET = var.function_entrypoint
    }
  )
}

# Use the common builder module for Docker image building
module "builder" {
  source = "../cloud_run_builder"

  service_name          = var.function_name
  service_type          = "function"
  build_context         = "cloud_run_function"
  artifact_registry_url = var.artifact_registry_url
  code_root_path        = var.function_root_path
  shared_path           = var.function_shared_path
  common_path           = var.function_common_path
  resources_path        = var.function_resources_path
  docker_file_path      = var.docker_file_path != null ? var.docker_file_path : "${local.module_dir}/Dockerfile"
  runtime_docker_image  = "python:3.12-slim"
  force_deploy          = var.force_deploy
}

resource "google_cloud_run_v2_service_iam_member" "sa_permissions" {
  for_each = toset([
    "roles/run.developer", "roles/run.invoker"
  ])
  role     = each.value
  project  = module.builder.gcp_project_id
  location = module.builder.gcp_region
  member   = "serviceAccount:${var.service_account_email}"
  name     = google_cloud_run_v2_service.this.name
}

resource "google_cloud_run_v2_service" "this" {
  provider   = google-beta
  depends_on = [module.builder]

  name                = var.function_name
  location            = module.builder.gcp_region
  labels              = var.labels
  deletion_protection = false

  template {
    service_account                  = var.service_account_email
    timeout                          = var.function_timeout
    max_instance_request_concurrency = var.max_instance_request_concurrency

    scaling {
      min_instance_count = var.min_instance_count
      max_instance_count = var.max_instance_count
    }

    dynamic "volumes" {
      for_each = var.bucket_volume != null ? [var.bucket_volume] : []
      content {
        name = "bucket"
        gcs {
          bucket    = var.bucket_volume.bucket_name
          read_only = false
        }
      }
    }

    dynamic "vpc_access" {
      for_each = var.vpc_access ? [var.vpc_access] : []
      content {
        network_interfaces {
          network    = "default"
          subnetwork = "default"
        }
      }
    }

    containers {
      image = module.builder.image_name_tag

      dynamic "volume_mounts" {
        for_each = var.bucket_volume != null ? [var.bucket_volume] : []
        content {
          name       = "bucket"
          mount_path = var.bucket_volume.mount_path
        }
      }

      resources {
        limits = {
          cpu    = var.container_cpu
          memory = var.container_memory
        }
      }

      dynamic "env" {
        for_each = local.function_env_vars
        iterator = envs
        content {
          name  = envs.key
          value = envs.value
        }
      }

      dynamic "env" {
        for_each = var.secret_variables != null ? var.secret_variables : {}
        iterator = envs
        content {
          name = envs.key
          value_source {
            secret_key_ref {
              secret  = envs.value
              version = "latest"
            }
          }
        }
      }

      ports {
        container_port = 8080
      }
    }
  }

  traffic {
    percent = 100
    type    = "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST"
  }
}

# PubSub trigger implementation using Push Subscription
resource "google_pubsub_subscription" "function_trigger" {
  count = var.pubsub_trigger != null ? 1 : 0

  name  = "${var.function_name}-trigger-subscription"
  topic = var.pubsub_trigger.topic_name

  ack_deadline_seconds       = var.pubsub_trigger.ack_deadline_seconds
  message_retention_duration = var.pubsub_trigger.message_retention_duration
  retain_acked_messages      = var.pubsub_trigger.retain_acked_messages
  enable_message_ordering    = var.pubsub_trigger.enable_message_ordering

  push_config {
    push_endpoint = google_cloud_run_v2_service.this.uri

    # Add authentication for the push endpoint
    oidc_token {
      service_account_email = var.service_account_email
    }

    # Set content type for Cloud Events format
    attributes = {
      x-goog-version = "v1"
    }
  }

  # Retry policy configuration
  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }

  labels = var.labels
}

# IAM permissions for PubSub to invoke the Cloud Run service
resource "google_cloud_run_v2_service_iam_member" "pubsub_invoker" {
  count = var.pubsub_trigger != null ? 1 : 0

  project  = module.builder.gcp_project_id
  location = module.builder.gcp_region
  name     = google_cloud_run_v2_service.this.name
  role     = "roles/run.invoker"
  member   = "serviceAccount:service-${data.google_project.project[0].number}@gcp-sa-pubsub.iam.gserviceaccount.com"
}

# Get project information for PubSub service account
data "google_project" "project" {
  count = var.pubsub_trigger != null ? 1 : 0
}

# Allow unauthenticated access if requested
resource "google_cloud_run_v2_service_iam_member" "public_access" {
  count = var.allow_unauthenticated ? 1 : 0

  project  = module.builder.gcp_project_id
  location = module.builder.gcp_region
  name     = google_cloud_run_v2_service.this.name
  role     = "roles/run.invoker"
  member   = "allUsers"
}
