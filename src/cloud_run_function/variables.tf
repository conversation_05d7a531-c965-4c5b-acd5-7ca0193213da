variable "function_name" {
  description = "A user-defined name of the function"
  type        = string
}

variable "function_entrypoint" {
  description = "Entrypoint Python function name"
  type        = string
  default     = "run"
}

variable "artifact_registry_url" {
  description = "GCP Artifact Registry URL"
  type        = string
}

variable "function_root_path" {
  description = "Function root directory path"
  type        = string
}

variable "function_shared_path" {
  description = "Function shared dependencies directory path"
  type        = string
  default     = null
}

variable "function_common_path" {
  description = "Function common dependencies directory path"
  type        = string
  default     = "../src/common"
}

variable "function_resources_path" {
  description = "Function extra resources directory path"
  type        = string
  default     = null
}

variable "docker_file_path" {
  description = "Docker file path, null means take default from the module"
  type        = string
  default     = null
}

variable "force_deploy" {
  description = "Allows to redeploy the code even if it has not been changed"
  type        = bool
  default     = false
}

variable "function_timeout" {
  description = "Function timeout, ref. https://cloud.google.com/run/docs/configuring/request-timeout"
  type        = string
  default     = "300s"
}

variable "service_account_email" {
  description = "Function service account email"
  type        = string
}

variable "container_cpu" {
  description = "Container CPU, ref. https://cloud.google.com/run/docs/configuring/cpu"
  type        = string
  default     = "1"
}

variable "container_memory" {
  description = "Container memory, ref. https://cloud.google.com/run/docs/configuring/memory-limits"
  type        = string
  default     = "512Mi"
}

variable "min_instance_count" {
  description = "Minimum number of instances, ref. https://cloud.google.com/run/docs/configuring/min-instances"
  type        = number
  default     = 0
}

variable "max_instance_count" {
  description = "Maximum number of instances, ref. https://cloud.google.com/run/docs/configuring/max-instances"
  type        = number
  default     = 1
}

variable "max_instance_request_concurrency" {
  description = "Maximum number of requests that can be processed simultaneously by each instance"
  type        = number
  default     = 1
}

variable "env_variables" {
  description = "A map of key/value environment variables"
  type        = map(string)
  default     = null
}

variable "secret_variables" {
  description = "A map of key/value where key is an environment variable name and value secret id"
  type        = map(string)
  default     = null
}

variable "bucket_volume" {
  description = "GCS bucket that will be mounted as a volume for container"
  type = object({
    bucket_name = string
    mount_path  = string
  })
  default = null
}

variable "vpc_access" {
  description = "Whether to create function in a default subnet of default VPC"
  type        = bool
  default     = false
}

variable "labels" {
  description = "A map of key/value labels"
  type        = map(string)
  default     = null
}

variable "allow_unauthenticated" {
  description = "Whether to allow unauthenticated access to the function"
  type        = bool
  default     = false
}

variable "pubsub_trigger" {
  description = "PubSub trigger configuration for the function"
  type = object({
    topic_name   = string
    retry_policy = optional(string, "RETRY_POLICY_DO_NOT_RETRY")
    # Additional PubSub subscription configuration
    ack_deadline_seconds       = optional(number, 600)
    message_retention_duration = optional(string, "604800s") # 7 days
    retain_acked_messages      = optional(bool, false)
    enable_message_ordering    = optional(bool, false)
  })
  default = null
}
