# Cloud Run Function

A module to abstract all configuration needed for deploying Python GCP Cloud Function Gen2 using Cloud Run v2 Service:
- Build and push Docker images to Artifact Registry
- Deploy as Cloud Function Gen2 using `google_cloud_run_v2_service` resource
- Support for shared and common dependencies
- Automatic code change detection and redeployment
- Proper IAM permissions and service account configuration
- Support for environment variables and secrets
- VPC access and bucket volume mounting capabilities

This module differs from the standard `cloud_function` module by using the Cloud Run v2 Service resource instead of the Cloud Functions v2 resource, providing more flexibility and control over the deployment.

## Key Features

- **Docker-based deployment**: Builds and pushes Docker images to Artifact Registry
- **Cloud Function Gen2 compatibility**: Uses proper annotations and environment variables
- **Automatic change detection**: Only rebuilds when source code changes
- **Flexible dependencies**: Support for shared and common code paths
- **Environment variables**: Support for both regular and secret environment variables
- **Resource configuration**: Configurable CPU, memory, and scaling settings
- **VPC access**: Optional VPC connectivity
- **Bucket volumes**: Optional GCS bucket mounting
- **PubSub triggers**: Support for PubSub message triggers using push subscriptions

## Usage

```hcl
module "my_function" {
  source = "./src/cloud_run_function"

  function_name         = "my-function"
  function_entrypoint   = "main"
  artifact_registry_url = "europe-west3-docker.pkg.dev/my-project/my-repo"
  function_root_path    = "./functions/my_function"
  service_account_email = "<EMAIL>"

  env_variables = {
    ENV = "production"
  }

  secret_variables = {
    API_KEY = "my-secret"
  }

  container_cpu    = "1"
  container_memory = "512Mi"

  labels = {
    environment = "production"
    team        = "data"
  }
}
```

### PubSub Triggered Function

```hcl
module "pubsub_function" {
  source = "./src/cloud_run_function"

  function_name         = "pubsub-function"
  function_entrypoint   = "handle_pubsub"
  artifact_registry_url = "europe-west3-docker.pkg.dev/my-project/my-repo"
  function_root_path    = "./functions/pubsub_function"
  service_account_email = "<EMAIL>"

  # PubSub trigger configuration
  pubsub_trigger = {
    topic_name               = "my-topic"
    ack_deadline_seconds     = 300
    retry_policy            = "RETRY_POLICY_RETRY"
    enable_message_ordering = true
  }

  labels = {
    environment = "production"
    trigger     = "pubsub"
  }
}
```

## Requirements

- Docker must be available in the build environment
- `gcloud` CLI must be configured for authentication
- Artifact Registry repository must exist
- Service account must have appropriate permissions

## Function Code Structure

### HTTP Functions

Your HTTP function code should follow the Cloud Functions framework structure:

```python
import functions_framework

@functions_framework.http
def main(request):
    """HTTP Cloud Function entry point."""
    return {"message": "Hello, World!"}
```

### PubSub Functions

For PubSub-triggered functions, use the Cloud Events format:

```python
import functions_framework
from cloudevents.http import CloudEvent

@functions_framework.cloud_event
def handle_pubsub(cloud_event: CloudEvent):
    """PubSub Cloud Function entry point."""
    import base64
    import json

    # Decode the PubSub message
    pubsub_message = base64.b64decode(cloud_event.data["message"]["data"]).decode()
    message_data = json.loads(pubsub_message)

    print(f"Received message: {message_data}")

    # Process the message
    # Your business logic here

    return "OK"
```

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |
| <a name="provider_google-beta"></a> [google-beta](#provider\_google-beta) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_builder"></a> [builder](#module\_builder) | ../cloud_run_builder | n/a |

## Resources

| Name | Type |
|------|------|
| [google-beta_google_cloud_run_v2_service.this](https://registry.terraform.io/providers/hashicorp/google-beta/latest/docs/resources/google_cloud_run_v2_service) | resource |
| [google_cloud_run_v2_service_iam_member.pubsub_invoker](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_v2_service_iam_member) | resource |
| [google_cloud_run_v2_service_iam_member.sa_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_v2_service_iam_member) | resource |
| [google_pubsub_subscription.function_trigger](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_subscription) | resource |
| [google_project.project](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/project) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_artifact_registry_url"></a> [artifact\_registry\_url](#input\_artifact\_registry\_url) | GCP Artifact Registry URL | `string` | n/a | yes |
| <a name="input_bucket_volume"></a> [bucket\_volume](#input\_bucket\_volume) | GCS bucket that will be mounted as a volume for container | <pre>object({<br/>    bucket_name = string<br/>    mount_path  = string<br/>  })</pre> | `null` | no |
| <a name="input_container_cpu"></a> [container\_cpu](#input\_container\_cpu) | Container CPU, ref. https://cloud.google.com/run/docs/configuring/cpu | `string` | `"1"` | no |
| <a name="input_container_memory"></a> [container\_memory](#input\_container\_memory) | Container memory, ref. https://cloud.google.com/run/docs/configuring/memory-limits | `string` | `"512Mi"` | no |
| <a name="input_docker_file_path"></a> [docker\_file\_path](#input\_docker\_file\_path) | Docker file path, null means take default from the module | `string` | `null` | no |
| <a name="input_env_variables"></a> [env\_variables](#input\_env\_variables) | A map of key/value environment variables | `map(string)` | `null` | no |
| <a name="input_force_deploy"></a> [force\_deploy](#input\_force\_deploy) | Allows to redeploy the code even if it has not been changed | `bool` | `false` | no |
| <a name="input_function_common_path"></a> [function\_common\_path](#input\_function\_common\_path) | Function common dependencies directory path | `string` | `"../src/common"` | no |
| <a name="input_function_entrypoint"></a> [function\_entrypoint](#input\_function\_entrypoint) | Entrypoint Python function name | `string` | `"run"` | no |
| <a name="input_function_name"></a> [function\_name](#input\_function\_name) | A user-defined name of the function | `string` | n/a | yes |
| <a name="input_function_resources_path"></a> [function\_resources\_path](#input\_function\_resources\_path) | Function extra resources directory path | `string` | `null` | no |
| <a name="input_function_root_path"></a> [function\_root\_path](#input\_function\_root\_path) | Function root directory path | `string` | n/a | yes |
| <a name="input_function_shared_path"></a> [function\_shared\_path](#input\_function\_shared\_path) | Function shared dependencies directory path | `string` | `null` | no |
| <a name="input_function_timeout"></a> [function\_timeout](#input\_function\_timeout) | Function timeout, ref. https://cloud.google.com/run/docs/configuring/request-timeout | `string` | `"300s"` | no |
| <a name="input_labels"></a> [labels](#input\_labels) | A map of key/value labels | `map(string)` | `null` | no |
| <a name="input_max_instance_count"></a> [max\_instance\_count](#input\_max\_instance\_count) | Maximum number of instances, ref. https://cloud.google.com/run/docs/configuring/max-instances | `number` | `1` | no |
| <a name="input_max_instance_request_concurrency"></a> [max\_instance\_request\_concurrency](#input\_max\_instance\_request\_concurrency) | Maximum number of requests that can be processed simultaneously by each instance | `number` | `1` | no |
| <a name="input_min_instance_count"></a> [min\_instance\_count](#input\_min\_instance\_count) | Minimum number of instances, ref. https://cloud.google.com/run/docs/configuring/min-instances | `number` | `0` | no |
| <a name="input_pubsub_trigger"></a> [pubsub\_trigger](#input\_pubsub\_trigger) | PubSub trigger configuration for the function | <pre>object({<br/>    topic_name   = string<br/>    retry_policy = optional(string, "RETRY_POLICY_DO_NOT_RETRY")<br/>    # Additional PubSub subscription configuration<br/>    ack_deadline_seconds       = optional(number, 600)<br/>    message_retention_duration = optional(string, "604800s") # 7 days<br/>    retain_acked_messages      = optional(bool, false)<br/>    enable_message_ordering    = optional(bool, false)<br/>  })</pre> | `null` | no |
| <a name="input_secret_variables"></a> [secret\_variables](#input\_secret\_variables) | A map of key/value where key is an environment variable name and value secret id | `map(string)` | `null` | no |
| <a name="input_service_account_email"></a> [service\_account\_email](#input\_service\_account\_email) | Function service account email | `string` | n/a | yes |
| <a name="input_vpc_access"></a> [vpc\_access](#input\_vpc\_access) | Whether to create function in a default subnet of default VPC | `bool` | `false` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_function_full_name"></a> [function\_full\_name](#output\_function\_full\_name) | Function full name, including GCP project id |
| <a name="output_function_id"></a> [function\_id](#output\_function\_id) | Function id |
| <a name="output_function_name"></a> [function\_name](#output\_function\_name) | Function name |
| <a name="output_function_url"></a> [function\_url](#output\_function\_url) | Function URL |
| <a name="output_pubsub_subscription_id"></a> [pubsub\_subscription\_id](#output\_pubsub\_subscription\_id) | PubSub subscription ID for the function trigger |
| <a name="output_pubsub_subscription_name"></a> [pubsub\_subscription\_name](#output\_pubsub\_subscription\_name) | PubSub subscription name for the function trigger |
<!-- END_TF_DOCS -->
