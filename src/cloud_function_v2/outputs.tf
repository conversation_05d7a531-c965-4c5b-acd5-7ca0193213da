output "function_id" {
  value       = google_cloud_run_v2_service.this.id
  description = "Function id"
}

output "function_name" {
  value       = google_cloud_run_v2_service.this.name
  description = "Function name"
}

output "function_url" {
  value       = google_cloud_run_v2_service.this.uri
  description = "Function URL"
}

output "function_full_name" {
  value       = "namespaces/${data.google_client_config.config.project}/services/${google_cloud_run_v2_service.this.name}"
  description = "Function full name, including GCP project id"
}
