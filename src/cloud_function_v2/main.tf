# GCP ref. https://cloud.google.com/run/docs/reference/rest/v2/projects.locations.services
# Terraform ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_v2_service

locals {
  runtime_docker_image       = "python:3.12-slim"
  gcp_project_id             = data.google_client_config.config.project
  gcp_region                 = data.google_client_config.config.region
  infra_dir                  = abspath(path.cwd)
  module_dir                 = abspath(path.module)
  tmp_root_dir               = "${local.infra_dir}/.temp/builds/cloud_function_v2"
  tmp_dir                    = "${local.tmp_root_dir}/${var.function_name}"
  tmp_dir_resources          = "${local.tmp_dir}/resources/"
  is_common_included         = var.function_common_path != null
  is_shared_included         = var.function_shared_path != null
  is_non_default_docker_file = var.docker_file_path != null
  is_resources_path_included = var.function_resources_path != null
  code_path                  = abspath(var.function_root_path)
  shared_path                = local.is_shared_included ? abspath(var.function_shared_path) : ""
  common_path                = local.is_common_included ? abspath(var.function_common_path) : ""
  resources_path             = local.is_resources_path_included ? abspath(var.function_resources_path) : ""
  docker_file_path           = local.is_non_default_docker_file ? abspath(var.docker_file_path) : "${local.module_dir}/Dockerfile"
  fileset_pattern            = "**"
  file_to_exclude            = "__pycache__"

  image_name     = "${var.artifact_registry_url}/${var.function_name}"
  image_name_tag = "${local.image_name}:${random_id.image_tag_suffix.dec}"

  run_on_change = { change = var.force_deploy ? uuid() : random_uuid.code_source_hash.result }

  # Cloud Function Gen2 specific environment variables
  function_env_vars = merge(
    var.env_variables != null ? var.env_variables : {},
    {
      FUNCTION_TARGET = var.function_entrypoint
    }
  )
}

data "google_client_config" "config" {}

resource "random_uuid" "code_source_hash" {
  keepers = {
    for file_path in setunion(
      [for name in fileset(var.function_root_path, local.fileset_pattern) : "${var.function_root_path}/${name}"],
      local.is_common_included ?
      [for name in fileset(var.function_common_path, local.fileset_pattern) : "${var.function_common_path}/${name}"] : [],
      local.is_shared_included ?
      [for name in fileset(var.function_shared_path, local.fileset_pattern) : "${var.function_shared_path}/${name}"] : [],
      local.is_resources_path_included ?
      [for name in fileset(var.function_resources_path, local.fileset_pattern) : "${var.function_resources_path}/${name}"] : [],
    ) :
    file_path => filemd5(file_path) if strcontains(file_path, local.file_to_exclude) == false
  }
}

resource "null_resource" "prepare_dependencies" {
  depends_on = [random_uuid.code_source_hash]

  provisioner "local-exec" {
    interpreter = ["bash", "-c"]
    command     = <<CMD
      set -euo pipefail
      rm -rf ${local.tmp_dir}
      mkdir -p ${local.tmp_dir}

      echo "Copying '${local.docker_file_path}' file..."
      cp -a ${local.docker_file_path} ${local.tmp_dir}

      echo "Copying '${local.code_path}' function dependencies..."
      cp -a ${local.code_path}/. ${local.tmp_dir}

      if [ -n "${local.shared_path}" ]; then
        echo "Copying '${local.shared_path}' function shared dependencies..."
        cp -a ${local.shared_path} ${local.tmp_dir}
      fi

      if [ -n "${local.common_path}" ]; then
        echo "Copying '${local.common_path}' function common dependencies..."
        cp -a ${local.common_path} ${local.tmp_dir}
      fi

      if [ -n "${local.resources_path}" ]; then
        echo "Copying '${local.resources_path}' function extra resources..."
        cp -a ${local.resources_path} ${local.tmp_dir_resources}
      fi

    CMD
  }

  triggers = local.run_on_change
}

resource "random_id" "image_tag_suffix" {
  depends_on  = [random_uuid.code_source_hash]
  byte_length = 4
  keepers     = local.run_on_change
}

resource "null_resource" "build_push_docker_image" {
  depends_on = [null_resource.prepare_dependencies]

  provisioner "local-exec" {
    interpreter = ["bash", "-c"]
    command     = <<CMD
      set -euo pipefail
      cd ${local.tmp_dir}
      echo "Building Docker image with '${local.image_name_tag}' tag..."
      docker build -q -t ${local.image_name_tag} --build-arg IMAGE_VERSION=${local.runtime_docker_image} .
      echo "Authenticate to GCP Artifact Registry..."
      gcloud auth configure-docker ${local.gcp_region}-docker.pkg.dev --quiet
      echo "Pushing Docker image with '${local.image_name_tag}' tag..."
      docker push ${local.image_name_tag}
    CMD
  }

  triggers = local.run_on_change
}

resource "google_cloud_run_v2_service_iam_member" "sa_permissions" {
  for_each = toset([
    "roles/run.developer", "roles/run.invoker"
  ])
  role     = each.value
  project  = local.gcp_project_id
  location = local.gcp_region
  member   = "serviceAccount:${var.service_account_email}"
  name     = google_cloud_run_v2_service.this.name
}

resource "google_cloud_run_v2_service" "this" {
  provider   = google-beta
  depends_on = [null_resource.build_push_docker_image]

  name                = var.function_name
  location            = local.gcp_region
  labels              = var.labels
  deletion_protection = false

  template {
    service_account                  = var.service_account_email
    timeout                          = var.function_timeout
    max_instance_request_concurrency = var.max_instance_request_concurrency

    scaling {
      min_instance_count = var.min_instance_count
      max_instance_count = var.max_instance_count
    }

    dynamic "volumes" {
      for_each = var.bucket_volume != null ? [var.bucket_volume] : []
      content {
        name = "bucket"
        gcs {
          bucket    = var.bucket_volume.bucket_name
          read_only = false
        }
      }
    }

    dynamic "vpc_access" {
      for_each = var.vpc_access ? [var.vpc_access] : []
      content {
        network_interfaces {
          network    = "default"
          subnetwork = "default"
        }
      }
    }

    containers {
      image = local.image_name_tag

      dynamic "volume_mounts" {
        for_each = var.bucket_volume != null ? [var.bucket_volume] : []
        content {
          name       = "bucket"
          mount_path = var.bucket_volume.mount_path
        }
      }

      resources {
        limits = {
          cpu    = var.container_cpu
          memory = var.container_memory
        }
      }

      dynamic "env" {
        for_each = local.function_env_vars
        iterator = envs
        content {
          name  = envs.key
          value = envs.value
        }
      }

      dynamic "env" {
        for_each = var.secret_variables != null ? var.secret_variables : {}
        iterator = envs
        content {
          name = envs.key
          value_source {
            secret_key_ref {
              secret  = envs.value
              version = "latest"
            }
          }
        }
      }

      ports {
        container_port = 8080
      }
    }
  }

  traffic {
    percent = 100
    type    = "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST"
  }
}
