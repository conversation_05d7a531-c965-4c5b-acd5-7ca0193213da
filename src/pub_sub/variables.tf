variable "topic_name" {
  description = "Name of the topic"
  type        = string
}

variable "sa_publishers" {
  description = "List of service account emails that will be granted the publisher role."
  type        = list(string)
  default     = []
}

variable "sa_subscribers" {
  description = "List of service account emails that will be granted the subscriber and viewer role."
  type        = list(string)
  default     = []
}

variable "should_create" {
  description = "Allows to create all necessary resources in a module. To be used for conditional creation of a module"
  type        = bool
  default     = true
}

variable "labels" {
  description = "A map of labels to apply to contained resources"
  type        = map(string)
  default     = {}
}
