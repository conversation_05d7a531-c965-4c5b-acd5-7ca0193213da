# Pub/Sub

A basic module to abstract Pub/Sub creation:
- Create a topic;
- (Optionally) Grant access to publishers;
- (Optionally) Grant access to subscribers.

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_pubsub_topic.topic](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic) | resource |
| [google_pubsub_topic_iam_member.topic_publishers](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic_iam_member) | resource |
| [google_pubsub_topic_iam_member.topic_subscribers](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic_iam_member) | resource |
| [google_project.project](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/project) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_labels"></a> [labels](#input\_labels) | A map of labels to apply to contained resources | `map(string)` | `{}` | no |
| <a name="input_sa_publishers"></a> [sa\_publishers](#input\_sa\_publishers) | List of service account emails that will be granted the publisher role. | `list(string)` | `[]` | no |
| <a name="input_sa_subscribers"></a> [sa\_subscribers](#input\_sa\_subscribers) | List of service account emails that will be granted the subscriber and viewer role. | `list(string)` | `[]` | no |
| <a name="input_should_create"></a> [should\_create](#input\_should\_create) | Allows to create all necessary resources in a module. To be used for conditional creation of a module | `bool` | `true` | no |
| <a name="input_topic_name"></a> [topic\_name](#input\_topic\_name) | Name of the topic | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_topic_id"></a> [topic\_id](#output\_topic\_id) | Pub/Sub topic id |
| <a name="output_topic_name"></a> [topic\_name](#output\_topic\_name) | Pub/Sub topic name |
<!-- END_TF_DOCS -->
