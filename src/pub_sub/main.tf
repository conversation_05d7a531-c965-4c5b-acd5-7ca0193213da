locals {
  publisher_roles  = toset(["roles/pubsub.publisher"])
  subscriber_roles = toset(["roles/pubsub.subscriber", "roles/pubsub.viewer"])

  publishers  = var.should_create ? var.sa_publishers : []
  subscribers = var.should_create ? var.sa_subscribers : []

  sa_publishers = merge([
    for idx, sa_id in local.publishers : {
      for role_id in local.publisher_roles :
      "${idx}-${role_id}" => { sa = sa_id, role = role_id }
    }
  ]...)

  sa_subscribers = merge([
    for idx, sa_id in local.subscribers : {
      for role_id in local.subscriber_roles :
      "${idx}-${role_id}" => { sa = sa_id, role = role_id }
    }
  ]...)
}

data "google_project" "project" {
}

# https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic
resource "google_pubsub_topic" "topic" {
  count  = var.should_create ? 1 : 0
  name   = var.topic_name
  labels = var.labels
}

# https://cloud.google.com/pubsub/docs/access-control
resource "google_pubsub_topic_iam_member" "topic_publishers" {
  for_each = local.sa_publishers
  project  = google_pubsub_topic.topic[0].project
  topic    = google_pubsub_topic.topic[0].name

  role   = each.value["role"]
  member = "serviceAccount:${each.value["sa"]}"
}

resource "google_pubsub_topic_iam_member" "topic_subscribers" {
  for_each = local.sa_subscribers
  project  = google_pubsub_topic.topic[0].project
  topic    = google_pubsub_topic.topic[0].name

  role   = each.value["role"]
  member = "serviceAccount:${each.value["sa"]}"
}
