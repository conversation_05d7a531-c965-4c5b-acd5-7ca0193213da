variable "function_name" {
  description = "A user-defined name of the function"
  type        = string
}

variable "function_description" {
  description = "Short description of the function"
  type        = string
  default     = null
}

variable "function_entrypoint" {
  description = "Entrypoint Python function name"
  type        = string
  default     = "run"
}

variable "function_root_path" {
  description = "Function root directory path"
  type        = string
}

variable "function_shared_path" {
  description = "Function shared dependencies directory path"
  type        = string
  default     = null
}

variable "common_path" {
  description = "Function common dependencies directory path"
  type        = string
  default     = "../src/common"
}

variable "force_deploy" {
  description = "Allows to redeploy the code even if it has not been changed"
  type        = bool
  default     = false
}

variable "artifact_bucket_name" {
  description = "Function zip artifact bucket name"
  type        = string
}

variable "service_account_email" {
  description = "Function service account email"
  type        = string
}

variable "event_trigger" {
  description = "Event triggers for the function"
  type = object({
    trigger_region = optional(string)
    event_type     = optional(string)
    pubsub_topic   = optional(string)
    # https://cloud.google.com/functions/docs/reference/rpc/google.cloud.functions.v2alpha#retrypolicy
    # https://cloud.google.com/functions/docs/bestpractices/retries
    retry_policy = optional(string, "RETRY_POLICY_DO_NOT_RETRY")
    event_filters = optional(set(object({
      attribute       = string
      attribute_value = string
      operator        = optional(string)
    })))
  })
  default = null
}

variable "service_config" {
  description = "Details of the service"
  type = object({
    max_instance_count    = optional(number)
    min_instance_count    = optional(number, 0)
    available_memory      = optional(string)
    available_cpu         = optional(string, null)
    timeout_seconds       = optional(number)
    runtime_env_variables = optional(map(string), null)
    runtime_secret_env_variables = optional(set(object({
      key_name   = string
      project_id = optional(string)
      secret     = string
      version    = string
    })), null)
    secret_volumes = optional(set(object({
      mount_path = string
      project_id = optional(string)
      secret     = string
      versions = set(object({
        version = string
        path    = string
      }))
    })), null)
    vpc_connector                  = optional(string, null)
    vpc_connector_egress_settings  = optional(string, null)
    ingress_settings               = optional(string, null)
    all_traffic_on_latest_revision = optional(bool, true)
  })
  default = {}
}

variable "labels" {
  description = "A set of key/value label pairs associated with this Cloud Function"
  type        = map(string)
  default     = null
}
