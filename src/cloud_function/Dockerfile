ARG IMAGE_VERSION

FROM $IMAGE_VERSION

# Set our working directory
WORKDIR /app

# Copy build files
COPY ./requirements.txt .

# Then install all dependencies
RUN python -m venv venv
RUN . venv/bin/activate
RUN pip install -r requirements.txt

# Install functions framework for Cloud Function Gen2 support
RUN pip install functions-framework

# Copy over the rest of the code
COPY . .

# https://stackoverflow.com/a/59812588
ENV PYTHONUNBUFFERED=1

# Expose port 8080 for Cloud Run
EXPOSE 8080

# Use functions-framework to start the function
CMD exec functions-framework --target=$FUNCTION_TARGET --port=8080
