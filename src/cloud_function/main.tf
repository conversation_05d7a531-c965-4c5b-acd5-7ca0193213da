# GCP ref. https://cloud.google.com/functions/docs/reference/rest/v2beta/projects.locations.functions
# Terraform ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloudfunctions2_function

locals {
  runtime              = "python312"
  runtime_docker_image = "python:3.12-slim"
  gcp_project_id       = data.google_client_config.config.project
  gcp_region           = data.google_client_config.config.region
  infra_dir            = abspath(path.cwd)
  module_dir           = abspath(path.module)
  tmp_root_dir         = "${local.infra_dir}/.temp/builds/cloud_function"
  tmp_dir              = "${local.tmp_root_dir}/${var.function_name}"
  code_path            = abspath(var.function_root_path)
  is_common_included   = var.common_path != null
  is_shared_included   = var.function_shared_path != null
  shared_path          = local.is_shared_included ? abspath(var.function_shared_path) : ""
  common_path          = local.is_common_included ? abspath(var.common_path) : ""
  fileset_pattern      = "**"
  file_to_exclude      = "__pycache__"

  docker_file_path = "${local.module_dir}/Dockerfile"
  # docker_repository is a Gen2 Cloud Function default repo artifacts
  # the name must be provided explicitly here, otherwise there is always a diff in terraform plan
  docker_repository = "projects/${local.gcp_project_id}/locations/${local.gcp_region}/repositories/gcf-artifacts"

  change_hash   = var.force_deploy ? uuid() : random_uuid.code_source_hash.result
  run_on_change = { change = local.change_hash }
}

data "google_client_config" "config" {}

resource "random_uuid" "code_source_hash" {
  keepers = {
    for file_path in setunion(
      [for name in fileset(var.function_root_path, local.fileset_pattern) : "${var.function_root_path}/${name}"],
      local.is_common_included ?
      [for name in fileset(var.common_path, local.fileset_pattern) : "${var.common_path}/${name}"] : [],
      local.is_shared_included ?
      [for name in fileset(var.function_shared_path, local.fileset_pattern) : "${var.function_shared_path}/${name}"] : [],
    ) :
    file_path => filemd5(file_path) if strcontains(file_path, local.file_to_exclude) == false
  }
}

resource "null_resource" "prepare_dependencies" {
  depends_on = [random_uuid.code_source_hash]

  provisioner "local-exec" {
    interpreter = ["bash", "-c"]
    command     = <<CMD
      set -euo pipefail
      rm -rf ${local.tmp_dir}
      mkdir -p ${local.tmp_dir}

      echo "Copying temporary Docker '${local.docker_file_path}' file..."
      cp -a ${local.docker_file_path} ${local.tmp_dir}

      echo "Copying '${local.code_path}' function dependencies..."
      cp -a ${local.code_path}/. ${local.tmp_dir}

      if [ -n "${local.shared_path}" ]; then
        echo "Copying '${local.shared_path}' function shared dependencies..."
        cp -a ${local.shared_path} ${local.tmp_dir}
      fi

      if [ -n "${local.common_path}" ]; then
        echo "Copying '${local.common_path}' function common dependencies..."
        cp -a ${local.common_path} ${local.tmp_dir}
      fi
    CMD
  }

  triggers = local.run_on_change
}

resource "null_resource" "install_dependencies" {
  depends_on = [null_resource.prepare_dependencies]

  provisioner "local-exec" {
    interpreter = ["bash", "-c"]
    command     = <<CMD
      set -euo pipefail
      cd ${local.tmp_dir}
      echo "Building Docker temporary '${var.function_name}' image and installing all pip dependencies..."
      docker build -q -t ${var.function_name} --build-arg IMAGE_VERSION=${local.runtime_docker_image} .
      echo "Copying all pip dependencies..."
      docker cp $(docker create --name ${var.function_name}-tmp ${var.function_name}):/app/. .
      docker rm ${var.function_name}-tmp
      rm -f requirements.txt Dockerfile
    CMD
  }

  triggers = local.run_on_change
}

resource "null_resource" "zip_dependencies" {
  depends_on = [null_resource.install_dependencies]

  provisioner "local-exec" {
    interpreter = ["bash", "-c"]
    command     = <<CMD
      set -euo pipefail
      cd ${local.tmp_dir}
      echo "Packaging all dependencies into zip archive..."
      find ./* ! -type d ! -path '*/__pycache__/*' | zip ${local.change_hash}.zip -@ > /dev/null
    CMD
  }

  triggers = local.run_on_change
}

resource "google_storage_bucket_object" "function_bucket_zip" {
  depends_on = [null_resource.zip_dependencies]

  name   = "${var.function_name}/${local.change_hash}.zip"
  bucket = var.artifact_bucket_name
  source = "${local.tmp_dir}/${local.change_hash}.zip"

  # to work around https://github.com/hashicorp/terraform-provider-google/issues/11138
  lifecycle {
    ignore_changes = [detect_md5hash]
  }
}

resource "google_cloudfunctions2_function" "this" {
  provider    = google-beta
  project     = local.gcp_project_id
  location    = local.gcp_region
  name        = var.function_name
  description = var.function_description

  build_config {
    runtime     = local.runtime
    entry_point = var.function_entrypoint

    source {
      storage_source {
        bucket = var.artifact_bucket_name
        object = google_storage_bucket_object.function_bucket_zip.name
      }
    }
    docker_repository = local.docker_repository
  }

  dynamic "event_trigger" {
    for_each = var.event_trigger != null ? [var.event_trigger] : []

    content {
      trigger_region        = local.gcp_region
      service_account_email = var.service_account_email
      pubsub_topic          = event_trigger.value["pubsub_topic"] != null ? event_trigger.value["pubsub_topic"] : null
      event_type            = event_trigger.value["event_type"] != null ? event_trigger.value["event_type"] : event_trigger.value["pubsub_topic"] != null ? "google.cloud.pubsub.topic.v1.messagePublished" : null

      retry_policy = event_trigger.value["retry_policy"]

      dynamic "event_filters" {
        for_each = event_trigger.value.event_filters != null ? event_trigger.value.event_filters : []
        content {
          attribute = event_filters.value.attribute
          value     = event_filters.value.attribute_value
          operator  = event_filters.value.operator
        }
      }
    }
  }

  dynamic "service_config" {
    for_each = var.service_config != null ? [var.service_config] : []
    content {
      max_instance_count    = service_config.value.max_instance_count
      min_instance_count    = service_config.value.min_instance_count
      available_memory      = service_config.value.available_memory
      available_cpu         = service_config.value.available_cpu
      timeout_seconds       = service_config.value.timeout_seconds
      environment_variables = service_config.value.runtime_env_variables != null ? service_config.value.runtime_env_variables : {}

      vpc_connector                 = service_config.value.vpc_connector
      vpc_connector_egress_settings = service_config.value.vpc_connector != null ? service_config.value.vpc_connector_egress_settings : null
      ingress_settings              = service_config.value.ingress_settings

      service_account_email          = var.service_account_email
      all_traffic_on_latest_revision = service_config.value.all_traffic_on_latest_revision

      dynamic "secret_environment_variables" {
        for_each = service_config.value.runtime_secret_env_variables != null ? service_config.value.runtime_secret_env_variables : []
        iterator = sev
        content {
          key        = sev.value.key_name
          project_id = sev.value.project_id
          secret     = sev.value.secret
          version    = sev.value.version
        }
      }

      dynamic "secret_volumes" {
        for_each = service_config.value.secret_volumes != null ? service_config.value.secret_volumes : []
        content {
          mount_path = secret_volumes.value.mount_path
          project_id = secret_volumes.value.project_id
          secret     = secret_volumes.value.secret
          dynamic "versions" {
            for_each = secret_volumes.value.versions != null ? secret_volumes.value.versions : []
            content {
              version = versions.value.version
              path    = versions.value.path
            }
          }
        }
      }
    }
  }

  labels = var.labels != null ? var.labels : {}
}

# Ref. https://github.com/hashicorp/terraform-provider-google/issues/15264
# IAM Binding for the generated cloud run service
resource "google_cloud_run_service_iam_member" "cloud_run_sa_permissions" {
  for_each = toset(["roles/run.invoker"]) # needed for event driven invocation
  role     = each.value
  project  = local.gcp_project_id
  location = local.gcp_region
  member   = "serviceAccount:${var.service_account_email}"
  service  = google_cloudfunctions2_function.this.name
}

# Ref. https://cloud.google.com/functions/docs/securing/authenticating
resource "google_cloudfunctions2_function_iam_member" "functions_sa_permissions" {
  for_each       = toset(["roles/cloudfunctions.invoker"]) # needed for gcloud command to invoke HTTP functions
  role           = each.value
  member         = "serviceAccount:${var.service_account_email}"
  project        = local.gcp_project_id
  location       = local.gcp_region
  cloud_function = google_cloudfunctions2_function.this.name
}
