# Cloud Function

A module to abstract all configuration needed for deploying Python GCP Cloud Function:
- install Python dependencies via `pip` only if `requirements.txt` file has changed,
- build project structure only if file the files definition has changed,
- zip code and all dependencies,
- upload to the dedicated storage bucket,
- create all cloud function resources including dedicated service account (principle of least privilege).

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |
| <a name="provider_google-beta"></a> [google-beta](#provider\_google-beta) | n/a |
| <a name="provider_null"></a> [null](#provider\_null) | n/a |
| <a name="provider_random"></a> [random](#provider\_random) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google-beta_google_cloudfunctions2_function.this](https://registry.terraform.io/providers/hashicorp/google-beta/latest/docs/resources/google_cloudfunctions2_function) | resource |
| [google_cloud_run_service_iam_member.cloud_run_sa_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_service_iam_member) | resource |
| [google_cloudfunctions2_function_iam_member.functions_sa_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloudfunctions2_function_iam_member) | resource |
| [google_storage_bucket_object.function_bucket_zip](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket_object) | resource |
| [null_resource.install_dependencies](https://registry.terraform.io/providers/hashicorp/null/latest/docs/resources/resource) | resource |
| [null_resource.prepare_dependencies](https://registry.terraform.io/providers/hashicorp/null/latest/docs/resources/resource) | resource |
| [null_resource.zip_dependencies](https://registry.terraform.io/providers/hashicorp/null/latest/docs/resources/resource) | resource |
| [random_uuid.code_source_hash](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/uuid) | resource |
| [google_client_config.config](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/client_config) | data source |
| [google_cloudfunctions2_function.data](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/cloudfunctions2_function) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_artifact_bucket_name"></a> [artifact\_bucket\_name](#input\_artifact\_bucket\_name) | Function zip artifact bucket name | `string` | n/a | yes |
| <a name="input_common_path"></a> [common\_path](#input\_common\_path) | Function common dependencies directory path | `string` | `"../src/common"` | no |
| <a name="input_event_trigger"></a> [event\_trigger](#input\_event\_trigger) | Event triggers for the function | <pre>object({<br/>    trigger_region = optional(string)<br/>    event_type     = optional(string)<br/>    pubsub_topic   = optional(string)<br/>    # https://cloud.google.com/functions/docs/reference/rpc/google.cloud.functions.v2alpha#retrypolicy<br/>    # https://cloud.google.com/functions/docs/bestpractices/retries<br/>    retry_policy = optional(string, "RETRY_POLICY_DO_NOT_RETRY")<br/>    event_filters = optional(set(object({<br/>      attribute       = string<br/>      attribute_value = string<br/>      operator        = optional(string)<br/>    })))<br/>  })</pre> | `null` | no |
| <a name="input_force_deploy"></a> [force\_deploy](#input\_force\_deploy) | Allows to redeploy the code even if it has not been changed | `bool` | `false` | no |
| <a name="input_function_description"></a> [function\_description](#input\_function\_description) | Short description of the function | `string` | `null` | no |
| <a name="input_function_entrypoint"></a> [function\_entrypoint](#input\_function\_entrypoint) | Entrypoint Python function name | `string` | `"run"` | no |
| <a name="input_function_name"></a> [function\_name](#input\_function\_name) | A user-defined name of the function | `string` | n/a | yes |
| <a name="input_function_root_path"></a> [function\_root\_path](#input\_function\_root\_path) | Function root directory path | `string` | n/a | yes |
| <a name="input_function_shared_path"></a> [function\_shared\_path](#input\_function\_shared\_path) | Function shared dependencies directory path | `string` | `null` | no |
| <a name="input_labels"></a> [labels](#input\_labels) | A set of key/value label pairs associated with this Cloud Function | `map(string)` | `null` | no |
| <a name="input_service_account_email"></a> [service\_account\_email](#input\_service\_account\_email) | Function service account email | `string` | n/a | yes |
| <a name="input_service_config"></a> [service\_config](#input\_service\_config) | Details of the service | <pre>object({<br/>    max_instance_count    = optional(number)<br/>    min_instance_count    = optional(number, 0)<br/>    available_memory      = optional(string)<br/>    available_cpu         = optional(string, null)<br/>    timeout_seconds       = optional(number)<br/>    runtime_env_variables = optional(map(string), null)<br/>    runtime_secret_env_variables = optional(set(object({<br/>      key_name   = string<br/>      project_id = optional(string)<br/>      secret     = string<br/>      version    = string<br/>    })), null)<br/>    secret_volumes = optional(set(object({<br/>      mount_path = string<br/>      project_id = optional(string)<br/>      secret     = string<br/>      versions = set(object({<br/>        version = string<br/>        path    = string<br/>      }))<br/>    })), null)<br/>    vpc_connector                  = optional(string, null)<br/>    vpc_connector_egress_settings  = optional(string, null)<br/>    ingress_settings               = optional(string, null)<br/>    all_traffic_on_latest_revision = optional(bool, true)<br/>  })</pre> | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_function_id"></a> [function\_id](#output\_function\_id) | Function id |
| <a name="output_function_name"></a> [function\_name](#output\_function\_name) | Function name |
| <a name="output_function_url"></a> [function\_url](#output\_function\_url) | Function url |
<!-- END_TF_DOCS -->
