# Storage Bucket

A simple module to abstract basic configuration for GCP storage bucket.
<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_storage_bucket.this](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket) | resource |
| [google_storage_bucket_iam_member.storage_bucket_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket_iam_member) | resource |
| [google_client_config.config](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/client_config) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_bucket_location"></a> [bucket\_location](#input\_bucket\_location) | GCP region, if null, taken from provider configuration. Ref. https://cloud.google.com/storage/docs/locations | `string` | `null` | no |
| <a name="input_bucket_name"></a> [bucket\_name](#input\_bucket\_name) | Bucket name | `string` | n/a | yes |
| <a name="input_bucket_public_access_prevention"></a> [bucket\_public\_access\_prevention](#input\_bucket\_public\_access\_prevention) | Ref. https://cloud.google.com/storage/docs/public-access-prevention | `string` | `"enforced"` | no |
| <a name="input_bucket_storage_class"></a> [bucket\_storage\_class](#input\_bucket\_storage\_class) | Ref. https://cloud.google.com/storage/docs/storage-classes | `string` | `"STANDARD"` | no |
| <a name="input_force_destroy"></a> [force\_destroy](#input\_force\_destroy) | Allows to delete non empty bucket | `bool` | `false` | no |
| <a name="input_labels"></a> [labels](#input\_labels) | A set of key/value label pairs associated with this module | `map(string)` | `null` | no |
| <a name="input_lifecycle_rules"></a> [lifecycle\_rules](#input\_lifecycle\_rules) | Lifecycle rules configuration | <pre>list(object({<br/>    # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket#nested_action<br/>    action = object({<br/>      type = string<br/>    })<br/>    # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket#nested_condition<br/>    condition = object({<br/>      age                = optional(string, null)<br/>      with_state         = optional(string, null)<br/>      matches_prefix     = optional(list(string), [])<br/>      num_newer_versions = optional(number, null)<br/>    })<br/>  }))</pre> | `[]` | no |
| <a name="input_permissions"></a> [permissions](#input\_permissions) | An optional permissions block to access bucket resources for given service account | <pre>list(object({<br/>    sa_email = string,<br/>    roles    = list(string)<br/>  }))</pre> | `[]` | no |
| <a name="input_should_create"></a> [should\_create](#input\_should\_create) | Allows to create all necessary resources in a module. To be used for conditional creation of a module | `bool` | `true` | no |
| <a name="input_versioning_enabled"></a> [versioning\_enabled](#input\_versioning\_enabled) | Enable versioning | `bool` | `false` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_bucket_name"></a> [bucket\_name](#output\_bucket\_name) | Bucket name |
| <a name="output_force_destroy"></a> [force\_destroy](#output\_force\_destroy) | Whether force destroy enabled |
| <a name="output_labels"></a> [labels](#output\_labels) | Bucket labels |
| <a name="output_public_access_prevention"></a> [public\_access\_prevention](#output\_public\_access\_prevention) | Whether public access prevention enabled |
| <a name="output_resource_name"></a> [resource\_name](#output\_resource\_name) | Resource name, ref. https://cloud.google.com/iam/docs/conditions-resource-attributes#resource-name |
| <a name="output_versioning_enabled"></a> [versioning\_enabled](#output\_versioning\_enabled) | Whether object versioning enabled |
<!-- END_TF_DOCS -->
