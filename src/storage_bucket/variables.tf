variable "bucket_name" {
  description = "Bucket name"
  type        = string
}

variable "bucket_location" {
  description = "GCP region, if null, taken from provider configuration. Ref. https://cloud.google.com/storage/docs/locations"
  type        = string
  default     = null
}

variable "bucket_storage_class" {
  description = "Ref. https://cloud.google.com/storage/docs/storage-classes"
  type        = string
  default     = "STANDARD"
}

variable "bucket_public_access_prevention" {
  description = "Ref. https://cloud.google.com/storage/docs/public-access-prevention"
  type        = string
  default     = "enforced" # means public access blocked
}

variable "permissions" {
  description = "An optional permissions block to access bucket resources for given service account"
  type = list(object({
    sa_email = string,
    roles    = list(string)
  }))
  default = []
}

variable "should_create" {
  description = "Allows to create all necessary resources in a module. To be used for conditional creation of a module"
  type        = bool
  default     = true
}

variable "force_destroy" {
  description = "Allows to delete non empty bucket"
  type        = bool
  default     = false
}

variable "labels" {
  description = "A set of key/value label pairs associated with this module"
  type        = map(string)
  default     = null
}

variable "versioning_enabled" {
  description = "Enable versioning"
  type        = bool
  default     = false
}

variable "lifecycle_rules" {
  description = "Lifecycle rules configuration"
  type = list(object({
    # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket#nested_action
    action = object({
      type = string
    })
    # https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket#nested_condition
    condition = object({
      age                = optional(string, null)
      with_state         = optional(string, null)
      matches_prefix     = optional(list(string), [])
      num_newer_versions = optional(number, null)
    })
  }))
  default = []
}
