locals {
  bucket_location = var.bucket_location != null ? var.bucket_location : data.google_client_config.config.region

  sa_roles = merge([
    for sa_id, item in var.permissions : {
      for role_id, role in item.roles :
      "${sa_id}-${role_id}" => { sa = item.sa_email, role = role }
    }
  ]...)
  bucket_permissions = var.should_create ? local.sa_roles : {}

  versioning_enabled_tuple = one(google_storage_bucket.this[*].versioning[*].enabled)
  versioning_enabled       = local.versioning_enabled_tuple != null ? one(local.versioning_enabled_tuple) : null
}

data "google_client_config" "config" {}

resource "google_storage_bucket" "this" {
  # By default, data is encrypted at rest at no additional charge.
  # Ref. https://cloud.google.com/storage/docs/encryption
  count                    = var.should_create ? 1 : 0
  name                     = var.bucket_name
  location                 = local.bucket_location
  storage_class            = var.bucket_storage_class
  public_access_prevention = var.bucket_public_access_prevention
  force_destroy            = var.force_destroy

  dynamic "lifecycle_rule" {
    for_each = var.lifecycle_rules
    iterator = lr
    content {
      action {
        type = lr.value.action.type
      }
      condition {
        age                = lr.value.condition.age
        with_state         = lr.value.condition.with_state
        matches_prefix     = lr.value.condition.matches_prefix
        num_newer_versions = lr.value.condition.num_newer_versions
      }
    }
  }

  versioning {
    enabled = var.versioning_enabled
  }

  labels = var.labels
}


resource "google_storage_bucket_iam_member" "storage_bucket_permissions" {
  for_each = local.bucket_permissions
  member   = "serviceAccount:${each.value["sa"]}"
  role     = each.value["role"]
  bucket   = google_storage_bucket.this[0].name
}
