locals {
  created_bucket_name = one(google_storage_bucket.this.*.name)
}

output "bucket_name" {
  value       = local.created_bucket_name
  description = "Bucket name"
}

output "resource_name" {
  value       = local.created_bucket_name != null ? "projects/_/buckets/${local.created_bucket_name}" : null
  description = "Resource name, ref. https://cloud.google.com/iam/docs/conditions-resource-attributes#resource-name"
}

output "versioning_enabled" {
  value       = local.versioning_enabled
  description = "Whether object versioning enabled"
}

output "force_destroy" {
  value       = one(google_storage_bucket.this.*.force_destroy)
  description = "Whether force destroy enabled"
}

output "public_access_prevention" {
  value       = one(google_storage_bucket.this.*.public_access_prevention)
  description = "Whether public access prevention enabled"
}

output "labels" {
  value       = one(google_storage_bucket.this.*.labels)
  description = "Bucket labels"
}
