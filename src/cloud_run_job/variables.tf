variable "job_name" {
  description = "A user-defined name of the job"
  type        = string
}

variable "artifact_registry_url" {
  description = "GCP Artifact Registry URL"
  type        = string
}

variable "job_root_path" {
  description = "Job root directory path"
  type        = string
}

variable "job_shared_path" {
  description = "Job shared dependencies directory path"
  type        = string
  default     = null
}

variable "job_common_path" {
  description = "Job common dependencies directory path"
  type        = string
  default     = "../src/common"
}

variable "job_resources_path" {
  description = "Job extra resources directory path"
  type        = string
  default     = null
}

variable "docker_file_path" {
  description = "Docker file path, null means take default from the module"
  type        = string
  default     = null
}

variable "force_deploy" {
  description = "Allows to redeploy the code even if it has not been changed"
  type        = bool
  default     = false
}

variable "job_timeout" {
  description = "Job timeout, ref. https://cloud.google.com/run/docs/configuring/task-timeout"
  type        = string
  default     = "600s"
}

variable "service_account_email" {
  description = "Job service account email"
  type        = string
}

variable "container_cpu" {
  description = "Container CPU, ref. https://cloud.google.com/run/docs/configuring/jobs/cpu"
  type        = number
  default     = 1
}

variable "container_memory" {
  description = "Container memory, ref. https://cloud.google.com/run/docs/configuring/jobs/memory-limits"
  type        = string
  default     = "512Mi"
}

variable "max_retries" {
  description = "Max retries, ref. https://cloud.google.com/run/docs/configuring/max-retries"
  type        = number
  default     = 0
}

variable "env_variables" {
  description = "A map of key/value environment variables"
  type        = map(string)
  default     = null
}

variable "secret_variables" {
  description = "A map of key/value where key is an environment variable name and value secret id"
  type        = map(string)
  default     = null
}

variable "bucket_volume" {
  description = "GCS bucket that will be mounted as a volume for container"
  type = object({
    bucket_name = string
    mount_path  = string
  })
  default = null
}

variable "vpc_access" {
  description = "Whether to create job in a default subnet of default VPC"
  type        = bool
  default     = false
}

variable "labels" {
  description = "A map of key/value labels"
  type        = map(string)
  default     = null
}
