# Cloud Run Job

A module to abstract all configuration needed for deploying Python GCP Cloud Run Job:
- build custom Docker image:
  - install Python dependencies via `pip` only if `requirements.txt` file has changed,
  - rebuild project structure only if file has changed.
- push Docker image to a given GCP Artifact Registry,
- create all Cloud Run Job related resources.

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |
| <a name="provider_google-beta"></a> [google-beta](#provider\_google-beta) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_builder"></a> [builder](#module\_builder) | ../cloud_run_builder | n/a |

## Resources

| Name | Type |
|------|------|
| [google-beta_google_cloud_run_v2_job.this](https://registry.terraform.io/providers/hashicorp/google-beta/latest/docs/resources/google_cloud_run_v2_job) | resource |
| [google_cloud_run_v2_job_iam_member.sa_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_v2_job_iam_member) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_artifact_registry_url"></a> [artifact\_registry\_url](#input\_artifact\_registry\_url) | GCP Artifact Registry URL | `string` | n/a | yes |
| <a name="input_bucket_volume"></a> [bucket\_volume](#input\_bucket\_volume) | GCS bucket that will be mounted as a volume for container | <pre>object({<br/>    bucket_name = string<br/>    mount_path  = string<br/>  })</pre> | `null` | no |
| <a name="input_container_cpu"></a> [container\_cpu](#input\_container\_cpu) | Container CPU, ref. https://cloud.google.com/run/docs/configuring/jobs/cpu | `number` | `1` | no |
| <a name="input_container_memory"></a> [container\_memory](#input\_container\_memory) | Container memory, ref. https://cloud.google.com/run/docs/configuring/jobs/memory-limits | `string` | `"512Mi"` | no |
| <a name="input_docker_file_path"></a> [docker\_file\_path](#input\_docker\_file\_path) | Docker file path, null means take default from the module | `string` | `null` | no |
| <a name="input_env_variables"></a> [env\_variables](#input\_env\_variables) | A map of key/value environment variables | `map(string)` | `null` | no |
| <a name="input_force_deploy"></a> [force\_deploy](#input\_force\_deploy) | Allows to redeploy the code even if it has not been changed | `bool` | `false` | no |
| <a name="input_job_common_path"></a> [job\_common\_path](#input\_job\_common\_path) | Job common dependencies directory path | `string` | `"../src/common"` | no |
| <a name="input_job_name"></a> [job\_name](#input\_job\_name) | A user-defined name of the job | `string` | n/a | yes |
| <a name="input_job_resources_path"></a> [job\_resources\_path](#input\_job\_resources\_path) | Job extra resources directory path | `string` | `null` | no |
| <a name="input_job_root_path"></a> [job\_root\_path](#input\_job\_root\_path) | Job root directory path | `string` | n/a | yes |
| <a name="input_job_shared_path"></a> [job\_shared\_path](#input\_job\_shared\_path) | Job shared dependencies directory path | `string` | `null` | no |
| <a name="input_job_timeout"></a> [job\_timeout](#input\_job\_timeout) | Job timeout, ref. https://cloud.google.com/run/docs/configuring/task-timeout | `string` | `"600s"` | no |
| <a name="input_labels"></a> [labels](#input\_labels) | A map of key/value labels | `map(string)` | `null` | no |
| <a name="input_max_retries"></a> [max\_retries](#input\_max\_retries) | Max retries, ref. https://cloud.google.com/run/docs/configuring/max-retries | `number` | `0` | no |
| <a name="input_secret_variables"></a> [secret\_variables](#input\_secret\_variables) | A map of key/value where key is an environment variable name and value secret id | `map(string)` | `null` | no |
| <a name="input_service_account_email"></a> [service\_account\_email](#input\_service\_account\_email) | Job service account email | `string` | n/a | yes |
| <a name="input_vpc_access"></a> [vpc\_access](#input\_vpc\_access) | Whether to create job in a default subnet of default VPC | `bool` | `false` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_job_full_name"></a> [job\_full\_name](#output\_job\_full\_name) | Job full name, including GCP project id |
| <a name="output_job_id"></a> [job\_id](#output\_job\_id) | Job id |
| <a name="output_job_name"></a> [job\_name](#output\_job\_name) | Job name |
<!-- END_TF_DOCS -->
