# GCP ref. https://cloud.google.com/run/docs/create-jobs
# Terraform ref. https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_v2_job

locals {
  module_dir = abspath(path.module)
}

# Use the common builder module for Docker image building
module "builder" {
  source = "../cloud_run_builder"

  service_name             = var.job_name
  service_type             = "job"
  build_context            = "cloud_run_job"
  artifact_registry_url    = var.artifact_registry_url
  code_root_path           = var.job_root_path
  shared_path              = var.job_shared_path
  common_path              = var.job_common_path
  resources_path           = var.job_resources_path
  docker_file_path         = var.docker_file_path
  default_docker_file_path = "${local.module_dir}/Dockerfile"
  runtime_docker_image     = "python:3.12-slim"
  force_deploy             = var.force_deploy
}



resource "google_cloud_run_v2_job_iam_member" "sa_permissions" {
  for_each = toset([
    "roles/run.developer", "roles/run.invoker"
  ])
  role     = each.value
  project  = module.builder.gcp_project_id
  location = module.builder.gcp_region
  member   = "serviceAccount:${var.service_account_email}"
  name     = google_cloud_run_v2_job.this.name
}


resource "google_cloud_run_v2_job" "this" {
  provider   = google-beta
  depends_on = [module.builder]

  name                = var.job_name
  location            = module.builder.gcp_region
  labels              = var.labels
  deletion_protection = false

  template {
    template {
      service_account = var.service_account_email
      timeout         = var.job_timeout
      max_retries     = var.max_retries

      dynamic "volumes" {
        for_each = var.bucket_volume != null ? [var.bucket_volume] : []
        content {
          name = "bucket"
          gcs {
            bucket    = var.bucket_volume.bucket_name
            read_only = false
          }
        }
      }

      dynamic "vpc_access" {
        for_each = var.vpc_access ? [var.vpc_access] : []
        content {
          network_interfaces {
            network    = "default"
            subnetwork = "default"
          }
        }
      }

      containers {
        image = module.builder.image_name_tag

        dynamic "volume_mounts" {
          for_each = var.bucket_volume != null ? [var.bucket_volume] : []
          content {
            name       = "bucket"
            mount_path = var.bucket_volume.mount_path
          }
        }

        resources {
          limits = {
            cpu    = var.container_cpu
            memory = var.container_memory
          }
        }

        dynamic "env" {
          for_each = var.env_variables != null ? var.env_variables : {}
          iterator = envs
          content {
            name  = envs.key
            value = envs.value
          }
        }

        dynamic "env" {
          for_each = var.secret_variables != null ? var.secret_variables : {}
          iterator = envs
          content {
            name = envs.key
            value_source {
              secret_key_ref {
                secret  = envs.value
                version = "latest"
              }
            }
          }
        }
      }
    }
  }
}
