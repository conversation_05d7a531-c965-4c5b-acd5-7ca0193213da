#!/bin/bash
set -euo pipefail

run_test() {
  local TEST_DIR="${1}"

  echo "Running '$TEST_DIR' tests..."
  cp terraform.tf "$TEST_DIR/terraform.tf"  > /dev/null
  terraform init -test-directory="$TEST_DIR" -backend-config="region=europe-west3" >/dev/null
  terraform test -verbose -test-directory="$TEST_DIR"
}

cleanup() {
  find tests -name "*\terraform.tf" -type f -delete > /dev/null
}
trap cleanup EXIT


main() {
  local BASE_DIR="${1}"
  local TEST_DIR=${2:-""}

  local PARENT_DIR=tests/"${BASE_DIR}"

  if [ -n "${TEST_DIR}" ]; then
      run_test "${PARENT_DIR}/${TEST_DIR}"
  else
    for dir in "${PARENT_DIR}"/*/
    do
      run_test "${dir}"
    done
  fi
}

main "$@"
