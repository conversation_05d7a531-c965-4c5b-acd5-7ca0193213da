locals {
  gcp_project_id = "refb-analytics-staging"
  gcp_region     = "europe-west3"
}

terraform {
  # https://developer.hashicorp.com/terraform/tutorials/configuration-language/test#prerequisites
  required_version = ">= v1.7.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.16.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 6.16.0"
    }
  }

  backend "gcs" {
    bucket = "refb-analytics-staging-tf-state"
    prefix = "infrastrure-modules"
  }
}

provider "google" {
  project = local.gcp_project_id
  region  = local.gcp_region
}
