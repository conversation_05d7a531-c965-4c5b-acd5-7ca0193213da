# GitLab infrastructure-modules set-up

This directory contains the necessary configurations to set up all permissions required for running tests within the CI/CD GitLab pipeline on the GCP project.

## Deploy the changes
```bash
make deploy-gitlab
```
## GitLab integration with GCP Artifact Registry

To use our custom image in GitLab, some prerequisites are needed:

* A dedicated service account with `roles/artifactregistry.reader` minimum permissions needed to pull the image from the registry,
* Git<PERSON>ab `DOCKER_AUTH_CONFIG` variable needed as described [here](https://docs.gitlab.com/ee/ci/docker/using_docker_images.html#determine-your-docker_auth_config-data),
* Credentials should be generated as described [here](https://cloud.google.com/artifact-registry/docs/docker/authentication?_gl=1*rfv51d*_ga******************************_ga_WH2QY8WWF5*********************************************&_ga=2.********.-**********.**********#json-key):
  * Generate and download locally service account key for `infra-modules-gitlab-runner`,
  * Run the following command:
    ```bash
    gcloud auth configure-docker europe-west3-docker.pkg.dev --quiet
    cat refb-analytics-staging.json | docker login -u _json_key --password-stdin https://europe-west3-docker.pkg.dev
    ```
  * Copy content of `~/.docker/config.json` into GitLab `DOCKER_AUTH_CONFIG` variable.
