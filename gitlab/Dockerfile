# This is image for infra builds.
FROM hashicorp/terraform:1.10 as tf110
FROM python:3.12-slim-bookworm AS python
FROM debian:12.9-slim

COPY --from=python / /
COPY --from=tf110 /bin/terraform /bin/terraform
RUN apt-get update -y && apt-get upgrade -y

RUN apt-get install -y curl bash
RUN curl https://sdk.cloud.google.com | bash

RUN apt-get install -y docker.io
RUN apt-get install -y zip make

ENV PATH="${PATH}:/usr/bin/:/root/google-cloud-sdk/bin"
ENV PYTHONUNBUFFERED=1
