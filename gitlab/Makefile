SHELL=../make.sh
$(DEBUG).SILENT: ; # no need for @, DEBUG=yes make ... disable silence
GCP_REGION=europe-west3

init-staging: ## Initializes terraform state for '**********************' GCP project
	terraform init -reconfigure -upgrade

deploy-gitlab: init-staging ## Deploys GitLab terraform changes to '**********************' GCP project
	terraform apply $(args)

gcloud-auth: ## Authenticate docker with GCP artifact registry
	gcloud auth configure-docker $(GCP_REGION)-docker.pkg.dev --quiet

IMAGE_TAG=$(GCP_REGION)-docker.pkg.dev/**********************/cicd-artifacts/infra-modules
push-cicd-image: gcloud-auth ## Builds and pushes CI/CD Docker images
	docker build -t $(IMAGE_TAG) . && docker push $(IMAGE_TAG)

# Auto-document our Makefile using a trick from https://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
.DEFAULT_GOAL := help
help: Makefile
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
