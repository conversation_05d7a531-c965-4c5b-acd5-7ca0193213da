# References:
# https://about.gitlab.com/blog/2023/06/28/introduction-of-oidc-modules-for-integration-between-google-cloud-and-gitlab-ci/
# https://gitlab.com/gitlab-com/gl-security/security-operations/infrastructure-security-public/oidc-modules/-/blob/main/samples/terraform/oidc-gcp/main.tf

locals {
  gitlab = {
    project_id   = ********
    project_name = "infrastructure-modules"
  }

  basic_labels = {
    owner = "data_engineering"
  }
}

module "cicd_gitlab" {
  source = "../src/service_account"

  sa_name = "${local.gitlab.project_name}-cicd"
  predefined_roles = [
    "roles/iam.roleAdmin",
    "roles/iam.serviceAccountUser",
    "roles/iam.serviceAccountAdmin",
    "roles/run.admin",
    "roles/cloudfunctions.admin",
    "roles/resourcemanager.projectIamAdmin",
    "roles/storage.admin",
    "roles/cloudsql.admin",
    "roles/secretmanager.admin",
    "roles/pubsub.admin",
    "roles/cloudscheduler.admin",
    "roles/artifactregistry.admin",
    "roles/artifactregistry.repoAdmin",
    "roles/bigquery.admin",
    "roles/logging.admin",
    "roles/monitoring.admin",
    "roles/workflows.admin"
  ]

  custom_permissions = [
    "iam.roles.get",
    "iam.workloadIdentityPools.get",
    "iam.workloadIdentityPoolProviders.get",
    "iam.workloadIdentityPoolProviders.update",
    "iam.serviceAccounts.getIamPolicy",
    "iam.serviceAccounts.getAccessToken",
    "resourcemanager.projects.getIamPolicy",
  ]

  labels = local.basic_labels
}

module "artifact_registry_gitlab" {
  source = "../src/service_account"

  sa_name          = "infra-modules-gitlab-runner"
  predefined_roles = ["roles/artifactregistry.reader"]

  labels = local.basic_labels
}

module "gitlab_oidc" {
  source                 = "gitlab.com/gitlab-com/gcp-oidc/google"
  version                = "3.2.1"
  google_project_id      = local.gcp_project_id
  workload_identity_name = "infra-modules"
  gitlab_project_id      = local.gitlab.project_id
  oidc_service_account = {
    "sa" = {
      sa_email  = module.cicd_gitlab.sa_email
      attribute = "attribute.project_id/${local.gitlab.project_id}"
    }
  }
}
